import axiosInstance from '@/middleware/axiosInstance';
import { LoginResponse } from '@/types/auth';
import { LoginFormData } from '@/validation/loginFormValidation';
import { SignupFormData } from '@/validation/signupFormValidation';
import { User } from '@/types/user';

const AUTH_URI_PREFIX = '/auth';

export const handleLoginIn = async (data: LoginFormData): Promise<LoginResponse> => {
    const response = await axiosInstance.post(`${AUTH_URI_PREFIX}/login`, data);
    return response.data;
};

export const handleSignup = async (
    data: Omit<SignupFormData, 'repeatPassword' | 'termsAndConditions'>
): Promise<void> => {
    await axiosInstance.post(`${AUTH_URI_PREFIX}/signup`, data);
};

export const handleTokenRefresh = async (refreshToken: string | null): Promise<LoginResponse> => {
    const response = await axiosInstance.post(`${AUTH_URI_PREFIX}/refresh`, {
        refreshToken,
    });
    return response.data;
};

const USER_AUTH_URI_PREFIX = '/user';

export const handleGetUser = async (): Promise<User> => {
    const response = await axiosInstance.get(`${USER_AUTH_URI_PREFIX}/me`);
    return response.data;
};
