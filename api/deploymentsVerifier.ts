import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import axiosInstance from '@/middleware/axiosInstance';
import { useConfigStore } from '@/store/configStore';
import { UpgradeDeploymentSecretResponse } from '@/types/deployments';
import {
    VerifierDeployment,
    VerifierDeploymentCreateRequest,
    VerifierDeploymentCreateResponse,
} from '@/types/deploymentsVerifier';
import { NewVerifierForm } from '@/validation/newVerifierValidation';

const { env } = useConfigStore.getState();
const deploymentType = DeploymentsTypesEnum[env.DEPLOYMENT_TYPE as keyof typeof DeploymentsTypesEnum];

const URI_PREFIX = '/deployment/verifier';

export const handleGetUserVerifierDeployments = async (): Promise<VerifierDeployment[]> => {
    const response = await axiosInstance.get(`${URI_PREFIX}/get/me`);
    return response.data;
};

export const handleGetVerifierDeploymentById = async (id: number): Promise<VerifierDeployment> => {
    const response = await axiosInstance.get(`${URI_PREFIX}/${id}`);
    return response.data;
};

export const handleCreateVerifierDeployment = async (
    props: NewVerifierForm
): Promise<VerifierDeploymentCreateResponse> => {
    const data: VerifierDeploymentCreateRequest = {
        ...props,
        deploymentType: deploymentType,
    };

    const response = await axiosInstance.post(`${URI_PREFIX}/create`, data);
    return response.data;
};

export const handleDeleteVerifierDeployment = async (id: number): Promise<void> => {
    const response = await axiosInstance.delete(`${URI_PREFIX}/${id}`);
    return response.data;
};

export const handleUpgradeVerifierDeploymentSecret = async (id: number): Promise<UpgradeDeploymentSecretResponse> => {
    const response = await axiosInstance.patch(`${URI_PREFIX}/${id}/update-client-secret`);
    return response.data;
};
