import axiosInstance from '@/middleware/axiosInstance';
import { LoginResponse } from '@/types/auth';

const URI_PREFIX = '/auth';

/**
 * Confirm a user’s e-mail address.
 *
 * @param email the user's email
 * @param token the confirmation token from the e-mail link
 * @returns plain-text success message from the server
 */
export const handleConfirmEmail = async (email: string, token: string): Promise<LoginResponse> => {
    const response = await axiosInstance.post(`${URI_PREFIX}/confirm-email`, { email, token });
    return response.data;
};

/**
 * Request a password-reset link to be sent to the user’s e-mail.
 *
 * @param email the account e-mail
 * @returns void (server responds with HTTP 200 and no body)
 */
export const handleRequestPasswordReset = async (email: string): Promise<void> => {
    await axiosInstance.post(`${URI_PREFIX}/request-reset`, { email });
};

/**
 * Reset a user’s password using the token from the reset link.
 *
 * @param email the user's email
 * @param token the reset token
 * @param newPassword the new password
 * @returns plain-text success message from the server
 */
export const handleResetPassword = async (email: string, token: string, newPassword: string): Promise<string> => {
    const { data } = await axiosInstance.post(`${URI_PREFIX}/reset-password`, {
        email,
        token,
        newPassword,
    });
    return data;
};

export const handleResendEmailConfirmation = async (email: string): Promise<void> => {
    await axiosInstance.post(`${URI_PREFIX}/resend-confirmation`, { email });
};
