import { DateTimeInput, PasswordInput, PinCodeInput, SelectInput, TextInput } from '@/components/FormFields';
import { camelToSnakeCase } from './camelToSnakeCase';
import { FormValidationError } from '@/components/FormValidationError';
import { Control, Controller, FieldValues } from 'react-hook-form';
import { FormFieldsTypes } from './formFieldsTypes';
import { SelectValue } from '@/types/selectValue';
import { Checkbox } from '@/components/Checkbox';

type FormFields<T> = {
    [K in keyof T]: any;
};

type RegisterFunction<T> = (field: keyof T) => any;

interface RenderFormFieldsProps<T extends FieldValues> {
    formFields: FormFields<T>;
    register: RegisterFunction<T>;
    errors: Partial<Record<keyof T, { message?: string }>>;
    t: (key: string) => string;
    formInputsTypes: Record<keyof T, FormFieldsTypes>;
    trigger?: (field: keyof T) => void;
    control?: Control<T>;
    selectOptions?: Record<any, Array<SelectValue>>;
    disabledFields?: Record<keyof T, boolean>;
    defaultValues?: Partial<T>;
}

export const renderFormFields = <T extends FieldValues>({
    formFields,
    register,
    errors,
    t,
    formInputsTypes,
    trigger,
    control,
    selectOptions,
    disabledFields,
    defaultValues,
}: RenderFormFieldsProps<T>) => {
    return Object.entries(formFields).map(([key]) => {
        const fieldKey = key as keyof T;
        const localeKey = camelToSnakeCase(key) + '_label';
        const fieldType = formInputsTypes[fieldKey];
        const handleBlur = trigger ? () => trigger(fieldKey) : undefined;
        const isDisabled = disabledFields?.[fieldKey] ?? false;

        if (fieldType === FormFieldsTypes.SELECT && selectOptions && selectOptions[fieldKey]) {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <SelectInput
                        {...register(fieldKey)}
                        label={t(localeKey)}
                        onBlur={handleBlur}
                        control={control}
                        options={selectOptions[fieldKey]}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.DATETIME) {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <DateTimeInput
                        {...register(fieldKey)}
                        label={t(localeKey)}
                        onBlur={handleBlur}
                        control={control}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.PASSWORD) {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <PasswordInput
                        {...register(fieldKey)}
                        label={t(localeKey)}
                        onBlur={handleBlur}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.PIN_CODE) {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <PinCodeInput
                        {...register(fieldKey)}
                        label={t(localeKey)}
                        onBlur={handleBlur}
                        defaultValue={defaultValues?.[fieldKey] as string}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.CHECKBOX) {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <Checkbox
                        {...register(fieldKey)}
                        mainLabel={t(localeKey)}
                        href="/signup/terms-and-conditions"
                        control={control}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        } else {
            return (
                <div key={key} className="flex flex-col gap-2">
                    <TextInput {...register(fieldKey)} label={t(localeKey)} onBlur={handleBlur} disabled={isDisabled} />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
    });
};
