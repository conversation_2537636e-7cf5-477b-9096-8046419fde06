{"name": "one-click-deployer-front", "version": "0.0.41", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check:locales": "node ./utils/checkLocales.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format:fix": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@stripe/stripe-js": "^7.3.1", "axios": "^1.7.7", "clsx": "^2.1.1", "jwt-decode": "^4.0.0", "next": "15.0.3", "next-intl": "^3.25.1", "prettier": "^3.5.3", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.53.2", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-router-dom": "^6.28.0", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@storybook/addon-essentials": "^8.4.2", "@storybook/addon-interactions": "^8.4.2", "@storybook/addon-onboarding": "^8.4.2", "@storybook/blocks": "^8.4.2", "@storybook/nextjs": "^8.4.2", "@storybook/react": "^8.4.2", "@storybook/test": "^8.4.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-plugin-storybook": "^0.11.0", "postcss": "^8", "storybook": "^8.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}