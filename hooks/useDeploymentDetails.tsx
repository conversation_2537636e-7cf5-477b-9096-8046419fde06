import { Deployment, DeploymentType } from '@/types/deployments';
import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { fetchDeploymentDetailsMethods } from '@/common/fetchMethods';
import { useErrorHandling } from './useErrorHandling';
import { ErrorGetDeploymentDetails } from '@/errors/ErrorGetDeploymentDetails';
import { useDeploymentDetailsStore } from '@/store/deploymentDetailsStore';

interface Props {
    type: DeploymentType;
}

export const useDeploymentDetails = ({ type }: Props) => {
    const { id } = useParams();
    const router = useRouter();
    const { withErrorHandling } = useErrorHandling();
    const { deploymentDetails: data, setDeploymentDetails } = useDeploymentDetailsStore();

    useEffect(() => {
        const handleGetData = () =>
            withErrorHandling(
                async () => {
                    try {
                        const numericId = Number(id);
                        const data = await fetchDeploymentDetailsMethods[type](numericId);
                        setDeploymentDetails(data);
                    } catch (error) {
                        throw new ErrorGetDeploymentDetails(error);
                    }
                },
                () => router.push('/dashboard')
            );

        handleGetData();
    }, [id]);

    return { data, id };
};
