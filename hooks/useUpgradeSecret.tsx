import { handleUpgradeIssuerDeploymentSecret } from '@/api/deploymentsIssuer';
import { handleUpgradeVerifierDeploymentSecret } from '@/api/deploymentsVerifier';
import { DeploymentType } from '@/types/deployments';
import { useModal } from './useModal';
import { ModalConfirmUpdateSecret, ModalSuccessUpdateSecret } from '@/components/Modals';
import { useErrorHandling } from './useErrorHandling';
import { ErrorUpdateDeploymentSecret } from '@/errors/ErrorUpdateDeploymentSecret';

interface Props {
    type: DeploymentType;
}

const fetchSecretMethods = {
    [DeploymentType.VERIFIER]: handleUpgradeVerifierDeploymentSecret,
    [DeploymentType.ISSUER]: handleUpgradeIssuerDeploymentSecret,
};

export const useUpgradeSecret = ({ type }: Props) => {
    const { showModal, hideModal } = useModal();
    const { withErrorHandling } = useErrorHandling();

    const handleUpgradeSecret = (id: number) =>
        withErrorHandling(async () => {
            try {
                const response = await fetchSecretMethods[type](id);
                showModal(<ModalSuccessUpdateSecret authKey={response.newClientSecret} />);
            } catch (error) {
                hideModal();
                throw new ErrorUpdateDeploymentSecret(error);
            }
        });

    const handleConfirmUpgradeSecret = (id: number) => {
        showModal(<ModalConfirmUpdateSecret onConfirm={() => handleUpgradeSecret(id)} />);
    };

    return { handleConfirmUpgradeSecret };
};
