import { SchemaAttribute, SchemaVersion } from '@/types/schemaManager';
import { SchemaFormData, createSchemaValidation } from '@/validation/issuerAdminSchemaManager';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export const useSchemaManager = () => {
    const [schemaVersion, setSchemaVersion] = useState<SchemaVersion>({
        name: '',
        version: '1.0',
        type: '',
        description: '',
        attributes: [],
    });
    const [editingAttribute, setEditingAttribute] = useState<string | null>(null);

    const {
        handleSubmit,
        formState: { isValid },
        watch,
        register,
        formState,
    } = useForm<SchemaFormData>({
        resolver: zodResolver(createSchemaValidation),
        defaultValues: {
            name: '',
            version: '1.0',
            type: '',
        },
    });

    const watchedName = watch('name');
    const watchedVersion = watch('version');
    const watchedType = watch('type');

    // Update schema version when form values change
    useEffect(() => {
        setSchemaVersion(prev => ({
            ...prev,
            name: watchedName || '',
            version: watchedVersion || '1.0',
            type: watchedType || '',
        }));
    }, [watchedName, watchedVersion, watchedType]);

    // Generate JSON Schema
    const generateJsonSchema = () => {
        const properties: Record<string, any> = {};
        const required: string[] = [];

        schemaVersion.attributes.forEach(attr => {
            properties[attr.name] = {
                type: attr.dataType,
                title: attr.title,
                description: attr.description || `Stores the ${attr.title.toLowerCase()} of the credential subject`,
            };

            if (attr.format) {
                properties[attr.name].format = attr.format;
            }

            if (attr.required) {
                required.push(attr.name);
            }
        });

        return {
            $metadata: {
                version: schemaVersion.version,
                type: schemaVersion.type,
            },
            title: schemaVersion.name,
            $schema: 'https://json-schema.org/draft/2020-12/schema',
            properties: {
                credentialSubject: {
                    description: `Stores the data of the ${schemaVersion.name.toLowerCase()}`,
                    title: `${schemaVersion.name} subject`,
                    properties,
                    required,
                    type: 'object',
                },
                '@context': {
                    type: ['string', 'array', 'object'],
                },
                id: {
                    type: 'string',
                },
                issuanceDate: {
                    format: 'date-time',
                    type: 'string',
                },
                issuer: {
                    type: ['string', 'object'],
                    format: 'uri',
                    properties: {
                        id: {
                            format: 'uri',
                            type: 'string',
                        },
                    },
                    required: ['id'],
                },
                type: {
                    type: ['string', 'array'],
                    items: {
                        type: 'string',
                    },
                },
                credentialSchema: {
                    properties: {
                        id: {
                            format: 'uri',
                            type: 'string',
                        },
                        type: {
                            type: 'string',
                        },
                    },
                    required: ['id', 'type'],
                    type: 'object',
                },
            },
            required: ['credentialSubject', '@context', 'id', 'issuanceDate', 'issuer', 'type', 'credentialSchema'],
            type: 'object',
        };
    };

    const addAttribute = () => {
        const newAttribute: SchemaAttribute = {
            id: `attr_${Date.now()}`,
            name: `attribute_${schemaVersion.attributes.length + 1}`,
            title: `Attribute ${schemaVersion.attributes.length + 1}`,
            dataType: 'string',
            description: '',
            required: false,
        };

        setSchemaVersion(prev => ({
            ...prev,
            attributes: [...prev.attributes, newAttribute],
        }));
        setEditingAttribute(newAttribute.id);
    };

    const removeAttribute = (id: string) => {
        setSchemaVersion(prev => ({
            ...prev,
            attributes: prev.attributes.filter(attr => attr.id !== id),
        }));
        if (editingAttribute === id) {
            setEditingAttribute(null);
        }
    };

    const updateAttribute = (id: string, updates: Partial<SchemaAttribute>) => {
        setSchemaVersion(prev => ({
            ...prev,
            attributes: prev.attributes.map(attr => (attr.id === id ? { ...attr, ...updates } : attr)),
        }));
    };

    const onSubmit = (_data: SchemaFormData) => {
        const jsonSchema = generateJsonSchema();
        console.log('Schema to publish:', jsonSchema);
        // Here you would typically send the schema to your API
    };

    return {
        schemaVersion,
        editingAttribute,
        isValid,
        setEditingAttribute,
        addAttribute,
        removeAttribute,
        generateJsonSchema,
        updateAttribute,
        handleSubmit,
        onSubmit,
        // Form control
        register,
        formState,
    };
};
