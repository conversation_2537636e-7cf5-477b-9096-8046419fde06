import { handleGetUser } from '@/api/user';
import { RouterPaths } from '@/common/routerPaths';
import { useDeploymentsStore } from '@/store/deploymentsStore';
import { useUserStore } from '@/store/userStore';
import { createNewIssuerSchema, NewIssuerForm } from '@/validation/newIssuerValidation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { useModal } from './useModal';
import { useTranslations } from 'next-intl';
import { handleCreateIssuerDeployment, handleGetUserIssuerDeployments } from '@/api/deploymentsIssuer';
import { ModalSuccessCreateNewIssuer } from '@/components/Modals';
import { useErrorHandling } from './useErrorHandling';
import { ErrorCreateNewIssuer } from '@/errors/ErrorCreateNewIssuer';

export const useNewIssuer = () => {
    const t = useTranslations();
    const schema = createNewIssuerSchema(t);
    const { withErrorHandling } = useErrorHandling();
    const { showModal } = useModal();
    const router = useRouter();
    const {
        register,
        handleSubmit,
        trigger,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<NewIssuerForm>({
        resolver: zodResolver(schema),
    });
    const formFields = schema.shape;
    const { setDeployments } = useDeploymentsStore();
    const { setUser } = useUserStore();

    const onSubmit = (data: NewIssuerForm) =>
        withErrorHandling(async () => {
            try {
                const response = await handleCreateIssuerDeployment(data);
                const deployments = await handleGetUserIssuerDeployments();
                const user = await handleGetUser();
                setDeployments(deployments);
                setUser(user);
                showModal(<ModalSuccessCreateNewIssuer authKey={response.authKey} />);
                router.push(RouterPaths.DASHBOARD);
            } catch (error) {
                throw new ErrorCreateNewIssuer(error);
            }
        });

    return {
        isSubmitting,
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        onSubmit,
        control,
    };
};
