import { ErrorUserLogin } from '@/errors/ErrorUserLogin';
import { ErrorTypes } from '@/common/errorTypes';
import { ErrorUserSignup } from '@/errors/ErrorUserSignup';
import { ErrorGetUserData } from '@/errors/ErrorGetUserData';
import { ErrorGetUserDeployments } from '@/errors/ErrorGetUserDeployments';
import { ErrorDeleteDeployment } from '@/errors/ErrorDeleteDeployment';
import { ErrorGetDeploymentDetails } from '@/errors/ErrorGetDeploymentDetails';
import { ErrorCreateNewIssuer } from '@/errors/ErrorCreateNewIssuer';
import { ErrorCreateNewVerifier } from '@/errors/ErrorCreateNewVerifier';
import { ErrorUpdateDeploymentSecret } from '@/errors/ErrorUpdateDeploymentSecret';
import { ErrorClassFields } from '@/types/errorClass';
import { ErrorToast } from '@/components/Toasts';
import { useToast } from './useToast';

const errorInstances = [
    (error: any) => error instanceof ErrorUserLogin,
    (error: any) => error instanceof ErrorUserSignup,
    (error: any) => error instanceof ErrorGetUserData,
    (error: any) => error instanceof ErrorGetUserDeployments,
    (error: any) => error instanceof ErrorDeleteDeployment,
    (error: any) => error instanceof ErrorGetDeploymentDetails,
    (error: any) => error instanceof ErrorCreateNewIssuer,
    (error: any) => error instanceof ErrorCreateNewVerifier,
    (error: any) => error instanceof ErrorUpdateDeploymentSecret,
];

export const useErrorHandling = () => {
    const { showToast } = useToast();

    const withErrorHandling = async <T,>(
        operation: () => Promise<T>,
        extraOnError?: () => void
    ): Promise<T | undefined> => {
        try {
            return await operation();
        } catch (error) {
            if (errorInstances.some(instance => instance(error))) {
                showToast(<ErrorToast {...(error as ErrorClassFields)} />);
            } else {
                showToast(<ErrorToast type={ErrorTypes.UNKNOWN} code={500} />);
            }

            if (extraOnError) {
                extraOnError();
            }
        }
    };

    return {
        withErrorHandling,
    };
};
