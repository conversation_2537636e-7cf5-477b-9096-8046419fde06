import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { useErrorHandling } from './useErrorHandling';
import { handleConfirmEmail } from '@/api/auth';
import { useAuthStore } from '@/store/authStore';
import { ConfirmEmailForm, createConfirmEmailSchema } from '@/validation/confirmEmailValidation';

interface UseConfirmEmailProps {
    email: string | null;
    token: string | null;
}

export const useConfirmEmail = ({ email, token }: UseConfirmEmailProps) => {
    const t = useTranslations();
    const { setAuthTokens } = useAuthStore();
    const schema = createConfirmEmailSchema(t);
    const { withErrorHandling } = useErrorHandling();
    const router = useRouter();

    const defaultValues = {
        token: token || undefined,
    };

    const {
        register,
        handleSubmit,
        control,
        setValue,
        watch,
        formState: { errors, isValid, isSubmitting },
    } = useForm<ConfirmEmailForm>({
        resolver: zodResolver(schema),
        mode: 'onChange',
        defaultValues,
    });

    const tokenFormValue = watch('token');

    const formFields = schema.shape;

    const handleFormSubmit = (data: ConfirmEmailForm) =>
        withErrorHandling(async () => {
            const { token: authToken, refreshToken } = await handleConfirmEmail(email!, data.token);
            setAuthTokens(authToken, refreshToken);
            router.replace(RouterPaths.DASHBOARD);
        });

    return {
        formFields,
        register,
        handleSubmit,
        errors,
        handleFormSubmit,
        isValid,
        isSubmitting,
        control,
        setValue,
        tokenFormValue,
        defaultValues,
    };
};
