'use client';

import { ItemBox } from '@/components/ItemBox';
import { ButtonGradient } from '@/components/Buttons';
import { useLoginForm } from '@/hooks/useLoginForm';
import { useTranslations } from 'next-intl';
import { renderFormFields } from '@/common/renderFormFields';
import { LoginFormDataInputsTypes } from '@/validation/loginFormValidation';
import { RouterPaths } from '@/common/routerPaths';
import Link from 'next/link';

const LOCALE_PREFIX = 'login_screen';

const LoginScreen = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { register, handleSubmit, errors, handleFormSubmit, trigger, isValid, formFields, isSubmitting } =
        useLoginForm();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: LoginFormDataInputsTypes,
        trigger,
    });

    return (
        <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
            <div className="w-full md:w-1/2">
                <ItemBox>
                    <div className="flex flex-col gap-10 py-8 md:px-24 px-8">
                        <p className="text-2xl">{t('title')}</p>
                        <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleFormSubmit)}>
                            <div className="flex flex-col gap-6">{fieldsToRender}</div>
                            <Link href={RouterPaths.REQUEST_PASSWORD_RESET} className="text-sm text-end text-main-100">
                                {t('request_password_reset')}
                            </Link>
                            <ButtonGradient type="submit" disabled={!isValid} isLoading={isSubmitting}>
                                {t('button')}
                            </ButtonGradient>
                        </form>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};
export default LoginScreen;
