'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { useRequestPasswordResetForm } from '@/hooks/useRequestPasswordResetForm';
import { RequestPasswordResetFormInputsTypes } from '@/validation/requestPasswordResetValidation';
import { ItemBox } from '@/components/ItemBox';
import Link from 'next/link';
import { RouterPaths } from '@/common/routerPaths';

const RequestPasswordReset = () => {
    const t = useTranslations('request_password_reset');
    const searchParams = useSearchParams();

    const emailFromParams = searchParams.get('email');

    const [requestSent, setRequestSent] = useState(false);
    const [submittedEmail, setSubmittedEmail] = useState<string | null>(emailFromParams);

    const { formFields, register, handleSubmit, errors, control, handleFormSubmit, isEmailDisabled } =
        useRequestPasswordResetForm({
            email: emailFromParams || undefined,
            onSuccess: data => {
                setSubmittedEmail(data.email);
                setRequestSent(true);
            },
        });

    if (requestSent) {
        return (
            <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
                <div className="w-full md:w-1/2">
                    <ItemBox>
                        <div className="py-8 md:px-24 px-4 text-left flex flex-col gap-6">
                            <h1 className="text-3xl font-bold">{t('success_title')}</h1>
                            <p className="mt-4 text-gray-300">
                                {t('success_description_part1')}
                                {submittedEmail && <span className="font-bold text-main-100"> {submittedEmail}</span>}
                            </p>
                        </div>
                    </ItemBox>
                </div>
            </div>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: RequestPasswordResetFormInputsTypes,
        control,
        disabledFields: {
            email: isEmailDisabled,
        },
    });

    const renderDescription = () => {
        if (isEmailDisabled) return null;
        return <p className="mt-2 text-center text-gray-300">{t('description')}</p>;
    };

    const renderFooter = () => {
        if (isEmailDisabled) return null;
        return (
            <div className="flex flex-row items-center justify-center whitespace-nowrap gap-1">
                Do you remember your password?
                <Link href={RouterPaths.LOGIN} className="text-main-100">
                    Return to login
                </Link>
            </div>
        );
    };

    return (
        <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
            <div className="w-full md:w-1/2">
                <ItemBox>
                    <div className="py-8 md:px-24 px-4">
                        <h1 className="text-3xl font-bold text-center">{t('title')}</h1>
                        {renderDescription()}
                        <form
                            onSubmit={handleSubmit(handleFormSubmit)}
                            className="flex text-left flex-col gap-4 p-6 mt-8 rounded-lg"
                            noValidate
                        >
                            {fieldsToRender}
                            <ButtonGradient type="submit">{t('button')}</ButtonGradient>
                        </form>
                        {renderFooter()}
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default RequestPasswordReset;
