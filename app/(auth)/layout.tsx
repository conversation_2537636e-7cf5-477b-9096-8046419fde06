import { Breadcrumb } from '@/components/Breadcrumb';
import { NavbarFlow } from '@/components/Navbar';

type SignupLayoutProps = {
    children: React.ReactNode;
};

export default async function SignupLayout({ children }: SignupLayoutProps) {
    return (
        <>
            <div className="w-full h-screen flex flex-col gap-4">
                <div className="py-4 md:py-0 flex flex-col gap-4 md:gap-0">
                    <NavbarFlow title="One Click Deployment" />
                    <div className="px-10">
                        <Breadcrumb />
                    </div>
                </div>
                {children}
            </div>
        </>
    );
}
