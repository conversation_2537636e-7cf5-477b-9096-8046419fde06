'use client';

import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import Link from 'next/link';
import { useSignupForm } from '@/hooks/useSignupForm';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { renderFormFields } from '@/common/renderFormFields';
import { SignupFormInputsTypes } from '@/validation/signupFormValidation';

const LOCALE_PREFIX = 'signup_screen';

const Signup = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { register, handleSubmit, errors, handleFormSubmit, trigger, isValid, formFields, isSubmitting, control } =
        useSignupForm();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: SignupFormInputsTypes,
        trigger,
        control,
    });

    return (
        <div className="flex flex-col items-center md:gap-28 px-8 justify-center h-screen overflow-y-hidden py-4">
            <div className="w-full md:w-1/2 overflow-hidden">
                <ItemBox>
                    <form
                        onSubmit={handleSubmit(handleFormSubmit)}
                        className="flex flex-col gap-8 md:gap-10 px-8 lg:px-24 md:py-6 py-3 overflow-auto without-scrollbar h-full"
                    >
                        <p className="text-2xl text-white">{t('title')}</p>
                        <div className="flex flex-col gap-6">{fieldsToRender}</div>
                        <ButtonGradient type="submit" disabled={!isValid} isLoading={isSubmitting}>
                            {t('button')}
                        </ButtonGradient>
                        <div className="flex flex-row gap-1 text-sm whitespace-nowrap">
                            <p className="text-white">{t('account_question')}</p>
                            <Link className="text-main-100" href={RouterPaths.LOGIN}>
                                {t('account_question_link')}
                            </Link>
                        </div>
                    </form>
                </ItemBox>
            </div>
        </div>
    );
};

export default Signup;
