'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { usePasswordReset } from '@/hooks/usePasswordResetForm';
import { PasswordResetFormInputsTypes } from '@/validation/passwordResetValidation';
import { ItemBox } from '@/components/ItemBox';

const ResetPassword = () => {
    const t = useTranslations('reset_password');
    const searchParams = useSearchParams();

    const email = searchParams.get('email');
    const token = searchParams.get('token');

    if (!email || !token) {
        throw new Error('Email and token are required for password reset');
    }

    const { formFields, register, handleSubmit, errors, control, handleFormSubmit } = usePasswordReset({
        email,
        token,
    });

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: PasswordResetFormInputsTypes,
        control,
    });

    return (
        <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
            <div className="w-full md:w-1/2">
                <ItemBox>
                    <div className="py-8 md:px-24 px-4">
                        <h1 className="text-3xl font-bold text-center">{t('title')}</h1>
                        <p className="mt-2 text-center text-gray-300">
                            {t('description_for_email')} <span className="font-bold">{email}</span>
                        </p>
                        <form
                            onSubmit={handleSubmit(handleFormSubmit)}
                            className="flex text-left flex-col gap-4 p-6 mt-8 rounded-lg"
                        >
                            {fieldsToRender}
                            <ButtonGradient type="submit">{t('button')}</ButtonGradient>
                        </form>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default ResetPassword;
