'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { useConfirmEmail } from '@/hooks/useConfirmEmailForm';
import { ConfirmEmailFormInputsTypes } from '@/validation/confirmEmailValidation';
import { handleResendEmailConfirmation } from '@/api/auth';
import { ItemBox } from '@/components/ItemBox';

const RESEND_TIMEOUT = 60; // seconds

const ConfirmEmail = () => {
    const t = useTranslations('confirm_email');
    const searchParams = useSearchParams();

    const [secondsLeft, setSecondsLeft] = useState(0);
    const [isResending, setIsResending] = useState(false);

    const email = searchParams.get('email');
    const token = searchParams.get('token');

    const {
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        control,
        handleFormSubmit,
        defaultValues,
    } = useConfirmEmail({ email, token });

    const onResend = async () => {
        if (!email) return;
        setIsResending(true);
        try {
            await handleResendEmailConfirmation(email);
            setSecondsLeft(RESEND_TIMEOUT);
        } finally {
            setIsResending(false);
        }
    };

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: ConfirmEmailFormInputsTypes,
        control,
        defaultValues,
    });

    return (
        <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
            <div className="w-full md:w-1/2">
                <ItemBox>
                    <div className="py-8 md:px-24 px-4">
                        <h1 className="text-3xl font-bold text-center">{t('title')}</h1>
                        <>
                            <p className="mt-2 text-center text-gray-300">
                                {t('description_for_email')} <span className="font-bold">{email}</span>
                            </p>
                            <form
                                onSubmit={handleSubmit(handleFormSubmit)}
                                className="flex text-left flex-col justify-center items-center gap-4 p-6 mt-8 rounded-lg"
                                noValidate
                            >
                                {fieldsToRender}
                                <ButtonGradient
                                    type="submit"
                                    disabled={!isValid || isSubmitting}
                                    isLoading={isSubmitting}
                                >
                                    {t('button')}
                                </ButtonGradient>
                                <ButtonGradient
                                    type="button"
                                    onClick={onResend}
                                    disabled={isResending || secondsLeft > 0}
                                    isLoading={isResending}
                                    className="mt-2"
                                >
                                    {secondsLeft > 0
                                        ? `To send new email wait ${secondsLeft} seconds`
                                        : 'Resend confirmation e-mail'}
                                </ButtonGradient>
                            </form>
                        </>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default ConfirmEmail;
