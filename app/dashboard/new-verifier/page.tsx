'use client';

import { renderFormFields } from '@/common/renderFormFields';
import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import { useGetAvailableBlockchainConfigs } from '@/hooks/useGetAvailableBlockchainConfigs';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { useNewVerifier } from '@/hooks/useNewVerifier';
import { DeploymentType } from '@/types/deployments';
import { NewVerifierFormInputsTypes } from '@/validation/newVerifierValidation';
import { useTranslations } from 'next-intl';
import { fetchDeploymentsMethods } from '@/common/fetchMethods';
import { useUserStore } from '@/store/userStore';
import { useEffect, useState } from 'react';
import { VerifierDeployment } from '@/types/deploymentsVerifier';
import { IssuerDeployment } from '@/types/deploymentsIssuer';
import { useDecoratedBlockchainConfigsSelect } from '@/hooks/useDecoratedBlockchainConfigs';

const LOCALE_PREFIX = 'new_verifier';

const NewVerifier = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { trigger, formFields, register, handleSubmit, errors, isValid, isSubmitting, onSubmit, control } =
        useNewVerifier();

    const { versions } = useGetAvailableVersions({
        type: DeploymentType.VERIFIER,
    });

    const { blockchainConfigs } = useGetAvailableBlockchainConfigs();
    const { user } = useUserStore();

    const [deployments, setDeployments] = useState<VerifierDeployment[] | IssuerDeployment[]>([]);

    useEffect(() => {
        (async () => {
            try {
                const data = await fetchDeploymentsMethods[DeploymentType.VERIFIER]();
                setDeployments(data);
            } catch (err) {
                console.error('Failed to fetch deployments', err);
            }
        })();
    }, []);

    const decoratedConfigs = useDecoratedBlockchainConfigsSelect(blockchainConfigs, user, 'VERIFIER', deployments);

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: NewVerifierFormInputsTypes,
        trigger,
        control,
        selectOptions: {
            versionId: versions,
            blockchainConfigurationId: decoratedConfigs,
        },
    });

    return (
        <div className="px-10 h-full flex justify-center items-center">
            <div className="w-full max-w-xl">
                <ItemBox>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8 p-6">
                        <h1 className="text-2xl text-center">{t('title')}</h1>
                        {fieldsToRender}
                        <ButtonGradient disabled={!isValid} isLoading={isSubmitting}>
                            {t('create_button')}
                        </ButtonGradient>
                    </form>
                </ItemBox>
            </div>
        </div>
    );
};

export default NewVerifier;
