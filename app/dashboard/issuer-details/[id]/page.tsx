'use client';

import { ItemBox } from '@/components/ItemBox';
import { DeploymentType } from '@/types/deployments';
import { WidgetDetailsOfDeployment } from '@/components/WidgetDetailsOfDeployment';
import { useDeleteDeployment } from '@/hooks/useDeleteDeployment';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useUpgradeSecret } from '@/hooks/useUpgradeSecret';
import { useUpgradeIssuerVersionModal } from '@/hooks/useUpgradeIssuerVersion';
import { LinkBorderSmall } from '@/components/Links/LinkBorderSmall';

const TYPE = DeploymentType.ISSUER;

const DetailsOfIssuerPage = () => {
    const { handleConfirmDelete } = useDeleteDeployment({ type: TYPE });
    const { data, id } = useDeploymentDetails({ type: TYPE });
    const { handleConfirmUpgradeSecret } = useUpgradeSecret({
        type: TYPE,
    });
    const { handleShowLogicUpgradeVersionModal } = useUpgradeIssuerVersionModal();

    if (!data) return null;
    return (
        <div className="flex flex-col items-center justify-center h-full px-4 md:px-8">
            <div className="w-full md:w-fit h-fit max-h-full">
                <ItemBox>
                    <WidgetDetailsOfDeployment
                        handleDelete={() => handleConfirmDelete(data.id, data.issuerName || '')}
                        handleUpgradeSecret={() => handleConfirmUpgradeSecret(data.id)}
                        handleUpgradeVersion={() => handleShowLogicUpgradeVersionModal(data.id, data.version)}
                        status={data.status}
                        type={TYPE}
                        deployment={data}
                        header={
                            <div className="self-center mx-auto pt-4 w-fit">
                                <LinkBorderSmall href={`/dashboard/issuer-details/${id}/issuer-admin`}>
                                    Issuer Admin Panel
                                </LinkBorderSmall>
                            </div>
                        }
                    />
                </ItemBox>
            </div>
        </div>
    );
};

export default DetailsOfIssuerPage;
