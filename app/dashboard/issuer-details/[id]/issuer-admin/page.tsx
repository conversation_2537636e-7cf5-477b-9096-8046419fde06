'use client';

import { ItemBox } from '@/components/ItemBox';
import { LinkGradientSmall } from '@/components/Links';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { DeploymentType } from '@/types/deployments';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const IssuerAdmin = () => {
    const { data, id } = useDeploymentDetails({ type: DeploymentType.ISSUER });
    const [schemas, setSchemas] = useState<Array<any>>([]);

    useEffect(() => {
        const handleGetSchemas = async () => {
            const schemas = await fetch(`${data!.fullHost}/api/v1/schemas`);
            console.log('🚀 ~ handleGetSchemas ~ schemas:', schemas);
            setSchemas(await schemas.json());
        };

        if (data?.id && data.fullHost) {
            handleGetSchemas();
        }
    }, [data]);

    return (
        <div className="flex flex-col items-center justify-center h-full px-4 md:px-8">
            <div className="w-full md:w-fit h-fit max-h-full">
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4">
                        <h1 className="text-xl self-center">Issuer Admin Page</h1>
                        <div className="self-center">
                            <LinkGradientSmall href={`/dashboard/issuer-details/${id}/issuer-admin/schema-builder`}>
                                Add new schema
                            </LinkGradientSmall>
                        </div>
                        <div className="flex flex-col gap-4">
                            <h1 className="self-center">Schema List</h1>
                            {schemas.map(schema => (
                                <div key={schema.id} className="flex flex-col border-main-200 rounded-lg border p-4">
                                    <span className="text-lg">{schema.name}</span>
                                    <span className="text-sm">{schema.type}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default IssuerAdmin;
