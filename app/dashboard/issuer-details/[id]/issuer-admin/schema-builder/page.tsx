'use client';

import { ItemBox } from '@/components/ItemBox';
import { ButtonBorder, ButtonBorderSmall, ButtonGradient, ButtonGradientSmall } from '@/components/Buttons';
import ReactJson from 'react-json-view';
import { DATA_TYPE_OPTIONS } from '@/const/SchemaPropertyType';
import { useSchemaManager } from '@/hooks/useSchemaManager';
import { SchemaAttribute } from '@/types/schemaManager';

const IssuerAdmin = () => {
    const {
        schemaVersion,
        editingAttribute,
        setEditingAttribute,
        addAttribute,
        removeAttribute,
        updateAttribute,
        handleSubmit,
        isValid,
        generateJsonSchema,
        onSubmit,
        register,
        formState,
    } = useSchemaManager();

    const renderIssuerDetails = () => {
        return (
            <div className="p-4 flex flex-col gap-4">
                <h2 className="text-lg font-semibold text-main-600 border-b border-main-800/20 pb-2">
                    Details of Issuer
                </h2>
                <div className="text-sm text-main-800">
                    <p>Configure your schema details and metadata</p>
                </div>
            </div>
        );
    };

    const renderDefineSchemaForm = () => {
        return (
            <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-main-600 mb-2">
                            Title <span className="text-red-400">*</span>
                        </label>
                        <input
                            {...register('name')}
                            type="text"
                            placeholder="Enter schema title"
                            className="w-full px-3 py-2 text-sm bg-main-900 border border-main-800 rounded text-main-600 placeholder-main-800 focus:outline-none focus:ring-2 focus:ring-main-500"
                        />
                        {formState.errors.name && (
                            <p className="text-red-400 text-xs mt-1">{formState.errors.name.message}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-main-600 mb-2">
                            Version <span className="text-red-400">*</span>
                        </label>
                        <input
                            {...register('version')}
                            type="text"
                            placeholder="1.0"
                            className="w-full px-3 py-2 text-sm bg-main-900 border border-main-800 rounded text-main-600 placeholder-main-800 focus:outline-none focus:ring-2 focus:ring-main-500"
                        />
                        {formState.errors.version && (
                            <p className="text-red-400 text-xs mt-1">{formState.errors.version.message}</p>
                        )}
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-main-600 mb-2">
                        Type <span className="text-red-400">*</span>
                    </label>
                    <input
                        {...register('type')}
                        type="text"
                        placeholder="Enter schema type"
                        className="w-full px-3 py-2 text-sm bg-main-900 border border-main-800 rounded text-main-600 placeholder-main-800 focus:outline-none focus:ring-2 focus:ring-main-500"
                    />
                    {formState.errors.type && (
                        <p className="text-red-400 text-xs mt-1">{formState.errors.type.message}</p>
                    )}
                </div>
            </div>
        );
    };

    const renderEditableAttribute = (attr: SchemaAttribute) => {
        return (
            <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                    <input
                        type="text"
                        value={attr.name}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                name: e.target.value,
                            })
                        }
                        placeholder="Attribute name"
                        className="px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                    />
                    <input
                        type="text"
                        value={attr.title}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                title: e.target.value,
                            })
                        }
                        placeholder="Title"
                        className="px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                    />
                </div>

                <select
                    value={attr.dataType}
                    onChange={e =>
                        updateAttribute(attr.id, {
                            dataType: e.target.value,
                        })
                    }
                    className="w-full px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                >
                    {DATA_TYPE_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>

                <textarea
                    value={attr.description || ''}
                    onChange={e =>
                        updateAttribute(attr.id, {
                            description: e.target.value,
                        })
                    }
                    placeholder="Description"
                    rows={2}
                    className="w-full px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600 resize-none"
                />

                <div className="flex items-center gap-2">
                    <input
                        type="checkbox"
                        checked={attr.required || false}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                required: e.target.checked,
                            })
                        }
                        className="w-4 h-4"
                    />
                    <span className="text-sm text-main-600">Required</span>
                </div>

                <div className="flex gap-2 h-8">
                    <ButtonGradientSmall onClick={() => setEditingAttribute(null)} type="button">
                        Save
                    </ButtonGradientSmall>
                    <ButtonBorderSmall onClick={() => removeAttribute(attr.id)} type="button">
                        Remove
                    </ButtonBorderSmall>
                </div>
            </div>
        );
    };

    const renderAttribute = (attr: SchemaAttribute) => {
        return (
            <div className="flex justify-between items-start">
                <div className="flex-1">
                    <div className="text-sm font-medium text-main-600">{attr.title}</div>
                    <div className="text-xs text-main-800">
                        {attr.name} ({attr.dataType}){attr.required && <span className="text-red-400 ml-1">*</span>}
                    </div>
                    {attr.description && <div className="text-xs text-main-800 mt-1">{attr.description}</div>}
                </div>
                <div className="flex gap-2">
                    <ButtonGradientSmall onClick={() => setEditingAttribute(attr.id)}>Edit</ButtonGradientSmall>
                    <ButtonBorderSmall onClick={() => removeAttribute(attr.id)}>Remove</ButtonBorderSmall>
                </div>
            </div>
        );
    };

    const renderActionButtonsFotter = () => {
        return (
            <div className="border-t border-main-800/20 pt-4 space-y-2">
                <ButtonGradient>Save</ButtonGradient>
                <ButtonBorder
                    onClick={handleSubmit(onSubmit)}
                    disabled={!isValid || schemaVersion.attributes.length === 0}
                >
                    Publish
                </ButtonBorder>
            </div>
        );
    };

    const renderJSONPreview = () => {
        return (
            <div className="p-4 flex flex-col gap-4 h-full">
                <div className="flex justify-between items-center border-b border-main-800/20 pb-2">
                    <h2 className="text-lg font-semibold text-main-600">JSON Preview</h2>
                </div>

                <div className="flex-1 overflow-auto without-scrollbar">
                    <div className="text-sm">
                        <div className="bg-main-1000/30 rounded p-3 font-mono text-xs">
                            <ReactJson
                                src={generateJsonSchema()}
                                theme="twilight"
                                style={{ background: 'transparent' }}
                                displayDataTypes={false}
                                collapsed={2}
                                name={false}
                                enableClipboard={true}
                            />
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderSchemaVersionFileTree = () => {
        const FolderIcon = () => (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-main-600">
                <path
                    d="M1.5 3.5C1.5 2.67157 2.17157 2 3 2H6.5L8 3.5H13C13.8284 3.5 14.5 4.17157 14.5 5V12.5C14.5 13.3284 13.8284 14 13 14H3C2.17157 14 1.5 13.3284 1.5 12.5V3.5Z"
                    stroke="currentColor"
                    strokeWidth="1.2"
                    fill="none"
                />
            </svg>
        );

        const FileIcon = () => (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-main-800">
                <path
                    d="M3 2C2.44772 2 2 2.44772 2 3V13C2 13.5523 2.44772 14 3 14H13C13.5523 14 14 13.5523 14 13V6L10 2H3Z"
                    stroke="currentColor"
                    strokeWidth="1.2"
                    fill="none"
                />
                <path d="M10 2V6H14" stroke="currentColor" strokeWidth="1.2" fill="none" />
            </svg>
        );

        const renderTreeItem = (attr: SchemaAttribute) => {
            const isEditing = editingAttribute === attr.id;

            return (
                <div
                    key={attr.id}
                    className={`flex items-center gap-2 py-1 px-2 rounded cursor-pointer transition-colors ${
                        isEditing ? 'bg-main-500/20 text-main-100' : 'hover:bg-main-800/30 text-main-600'
                    }`}
                    onClick={() => setEditingAttribute(attr.id)}
                >
                    <FileIcon />
                    <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{attr.title || attr.name}</div>
                        <div className="text-xs text-main-800 truncate">
                            {attr.name} ({attr.dataType}){attr.required && <span className="text-red-400 ml-1">*</span>}
                        </div>
                    </div>
                </div>
            );
        };

        return (
            <div className="p-4 flex flex-col gap-4 flex-[2]">
                <h2 className="text-lg font-semibold text-main-600 border-b border-main-800/20 pb-2">
                    Schema Versions
                </h2>

                {schemaVersion.attributes.length > 0 ? (
                    <div className="flex flex-col gap-1">
                        {/* Schema root folder */}
                        <div className="flex items-center gap-2 py-1 px-2 text-main-600">
                            <FolderIcon />
                            <div className="text-sm font-medium">
                                {schemaVersion.name || 'Schema'} v{schemaVersion.version}
                            </div>
                        </div>

                        {/* Credential Subject folder */}
                        <div className="flex items-center gap-2 py-1 px-2 ml-4 text-main-600">
                            <FolderIcon />
                            <div className="text-sm font-medium">credentialSubject</div>
                        </div>

                        {/* Attributes as files */}
                        <div className="ml-8 flex flex-col gap-1">{schemaVersion.attributes.map(renderTreeItem)}</div>
                    </div>
                ) : (
                    <div className="text-center text-main-800 py-8">
                        <div className="text-sm">No attributes added yet</div>
                        <div className="text-xs mt-1">Add attributes to see them in the tree view</div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="flex flex-row items-start gap-4 justify-center h-full px-4 md:px-8 py-4">
            <div className="flex-1 flex flex-col h-full gap-4">
                <div className="flex-1">
                    <ItemBox>{renderIssuerDetails()}</ItemBox>
                </div>

                {/* Schema Versions */}
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4 h-full">
                        <div className="flex justify-between items-center border-b border-main-800/20 pb-2">
                            <h2 className="text-lg font-semibold text-main-600">Define schema</h2>
                        </div>
                        {renderDefineSchemaForm()}

                        <div className="flex justify-between items-center border-b border-main-800/20 pb-2">
                            <h2 className="text-lg font-semibold text-main-600">Schema Versions</h2>
                        </div>

                        {/* Attribute List */}
                        <div className="flex flex-col flex-1 overflow-auto gap-4 without-scrollbar">
                            {schemaVersion.attributes.map(attr => (
                                <div key={attr.id} className="border border-main-800/30 rounded p-3 bg-main-1000/30">
                                    {editingAttribute === attr.id
                                        ? renderEditableAttribute(attr)
                                        : renderAttribute(attr)}
                                </div>
                            ))}
                        </div>
                        <button
                            onClick={addAttribute}
                            className="w-full p-2 border-2 border-dashed border-main-800 rounded text-main-600 hover:border-main-600 hover:text-main-100 flex items-center justify-center gap-2"
                        >
                            <span className="text-lg">+</span>
                            Add next attribute
                        </button>

                        {renderActionButtonsFotter()}
                    </div>
                </ItemBox>
            </div>

            <div className="flex-1 flex flex-col h-full gap-4">
                {/* Schema Display - Tree View */}
                <ItemBox>{renderSchemaVersionFileTree()}</ItemBox>

                {/* JSON Preview */}
                <ItemBox>{renderJSONPreview()}</ItemBox>
            </div>
        </div>
    );
};

export default IssuerAdmin;
