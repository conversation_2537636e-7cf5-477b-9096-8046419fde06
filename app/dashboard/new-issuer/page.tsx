'use client';

import { renderFormFields } from '@/common/renderFormFields';
import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import { useGetAvailableBlockchainConfigs } from '@/hooks/useGetAvailableBlockchainConfigs';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { useNewIssuer } from '@/hooks/useNewIssuer';
import { DeploymentType } from '@/types/deployments';
import { NewIssuerFormInputsTypes } from '@/validation/newIssuerValidation';
import { useTranslations } from 'next-intl';
import { useUserStore } from '@/store/userStore';
import { useEffect, useState } from 'react';
import { VerifierDeployment } from '@/types/deploymentsVerifier';
import { IssuerDeployment } from '@/types/deploymentsIssuer';
import { fetchDeploymentsMethods } from '@/common/fetchMethods';
import { useDecoratedBlockchainConfigsSelect } from '@/hooks/useDecoratedBlockchainConfigs';

const LOCALE_PREFIX = 'new_issuer';

const NewIssuer = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { trigger, formFields, register, handleSubmit, errors, isValid, onSubmit, isSubmitting, control } =
        useNewIssuer();

    const { versions } = useGetAvailableVersions({
        type: DeploymentType.ISSUER,
    });

    const { blockchainConfigs } = useGetAvailableBlockchainConfigs();

    const { user } = useUserStore();

    const [deployments, setDeployments] = useState<VerifierDeployment[] | IssuerDeployment[]>([]);

    useEffect(() => {
        (async () => {
            try {
                const data = await fetchDeploymentsMethods[DeploymentType.ISSUER]();
                setDeployments(data);
            } catch (err) {
                console.error('Failed to fetch deployments', err);
            }
        })();
    }, []);

    const decoratedConfigs = useDecoratedBlockchainConfigsSelect(blockchainConfigs, user, 'ISSUER', deployments);

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: NewIssuerFormInputsTypes,
        trigger,
        control,
        selectOptions: {
            versionId: versions,
            blockchainConfigurationId: decoratedConfigs,
        },
    });

    return (
        <div className="px-10 h-full flex justify-center items-center">
            <div className="w-full max-w-xl">
                <ItemBox>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8 p-6">
                        <h1 className="text-2xl text-center">{t('title')}</h1>
                        {fieldsToRender}
                        <ButtonGradient disabled={!isValid} isLoading={isSubmitting}>
                            {t('create_button')}
                        </ButtonGradient>
                    </form>
                </ItemBox>
            </div>
        </div>
    );
};

export default NewIssuer;
