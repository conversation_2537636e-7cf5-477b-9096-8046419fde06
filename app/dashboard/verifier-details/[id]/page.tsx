'use client';

import { ItemBox } from '@/components/ItemBox';
import { DeploymentType } from '@/types/deployments';
import { WidgetDetailsOfDeployment } from '@/components/WidgetDetailsOfDeployment';
import { useDeleteDeployment } from '@/hooks/useDeleteDeployment';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useUpgradeSecret } from '@/hooks/useUpgradeSecret';
import { useUpgradeVerifierVersionModal } from '@/hooks/useUpgradeVerifierVersion';

const TYPE = DeploymentType.VERIFIER;

const DetailsOfVerifierPage = () => {
    const { handleConfirmDelete } = useDeleteDeployment({
        type: TYPE,
    });
    const { data } = useDeploymentDetails({ type: TYPE });
    const { handleConfirmUpgradeSecret } = useUpgradeSecret({
        type: TYPE,
    });
    const { handleShowLogicUpgradeVersionModal } = useUpgradeVerifierVersionModal();

    if (!data) return null;
    return (
        <div className="flex flex-col items-center justify-center h-full px-4 md:px-8">
            <div className="w-full md:w-fit h-fit max-h-full">
                <ItemBox>
                    <WidgetDetailsOfDeployment
                        handleDelete={() => handleConfirmDelete(data.id, data.verifierName || '')}
                        handleUpgradeSecret={() => handleConfirmUpgradeSecret(data.id)}
                        handleUpgradeVersion={() => handleShowLogicUpgradeVersionModal(data.id, data.version)}
                        status={data.status}
                        type={TYPE}
                        deployment={data}
                    />
                </ItemBox>
            </div>
        </div>
    );
};

export default DetailsOfVerifierPage;
