import { DashboardHeader } from '@/components/DashboardHeader';
import { SideBar } from '@/components/SideBar';
import { DeploymentsTableTabsProvider } from '@/contexts/DeploymentsTableTabsContext';
import { SideBarProvider } from '@/contexts/SideBarContext';

export default async function DashboardLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <div className="flex flex-row">
            <SideBarProvider>
                <div className="flex flex-col flex-[9] h-screen">
                    <div className="w-full">
                        <DashboardHeader />
                    </div>
                    <DeploymentsTableTabsProvider>
                        <div className="overflow-auto h-full without-scrollbar pb-4">{children}</div>
                    </DeploymentsTableTabsProvider>
                </div>
                <SideBar />
            </SideBarProvider>
        </div>
    );
}
