'use client';
import { ItemBox } from '@/components/ItemBox';
import { WidgetDeploymentsTable } from '@/components/WidgetDeploymentsTable/WidgetDeploymentsTable';
import { WidgetNewDeployment } from '@/components/WidgetNewDeployment';
import { useDashboardLogic } from '@/hooks/useDashboardLogic';
import clsx from 'clsx';
import Image from 'next/image';
import { useState } from 'react';

const Dashboard = () => {
    const { deployments, isLoading } = useDashboardLogic();
    const [showNewDeployment, setShowNewDeployment] = useState(false);

    return (
        <div className="relative overflow-hidden w-full h-full">
            {/* <div
                className="block md:hidden !bg-main-1000 text-main-600 border-gradient rounded-lg mx-4 py-2 mb-4"
                onClick={() => setShowNewDeployment(!showNewDeployment)}
            >
                <div
                    className={clsx(
                        'pointer-events-none h-full text-center flex flex-row px-4',
                        showNewDeployment ? 'justify-end' : 'justify-between'
                    )}
                >
                    <span style={{ display: showNewDeployment ? 'none' : 'block' }}>New Deployment</span>
                    <Image
                        src="/assets/ArrowLeft.svg"
                        alt="arrow down"
                        width={24}
                        height={24}
                        className={clsx(showNewDeployment ? 'rotate-90' : '-rotate-90')}
                    />
                </div>
                <div className="text-center" style={{ display: showNewDeployment ? 'block' : 'none' }}>
                    <WidgetNewDeployment />
                </div>
            </div> */}

            {/* <div className="flex flex-col lg:flex-row gap-4 h-fit max-h-full px-4 md:px-10 pt-8"> */}
            <div className={clsx('flex flex-[2] pl-8 relative', showNewDeployment ? 'md:pr-[332px]' : 'md:pr-16')}>
                <ItemBox>
                    <WidgetDeploymentsTable isLoading={isLoading} deployments={deployments} />
                </ItemBox>
            </div>
            {/* </div> */}

            <div
                className="!absolute border-gradient -right-1 top-0 cursor-pointer h-64 gap-4 py-6 px-2 z-20 md:flex flex-row items-center justify-center !bg-main-1000 text-main-600 rounded-l-lg"
                onClick={() => setShowNewDeployment(!showNewDeployment)}
            >
                <div
                    className={clsx('pointer-events-none h-full text-center flex justify-between cursor-pointer')}
                    style={{ writingMode: 'sideways-lr', display: showNewDeployment ? 'none' : 'flex' }}
                >
                    <span>New Deployment</span>
                    <Image
                        src="/assets/ArrowLeft.svg"
                        alt="arrow down"
                        width={24}
                        height={24}
                        className={clsx(showNewDeployment && 'rotate-180')}
                    />
                </div>
                <div className="text-center" style={{ display: showNewDeployment ? 'block' : 'none' }}>
                    <WidgetNewDeployment />
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
