import { RouterPaths } from '@/common/routerPaths';
import { LinkBorder, LinkGradient } from '@/components/Links';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

const StartScreen = () => {
    const t = useTranslations();

    return (
        <div className="flex flex-col items-center gap-28 px-8 justify-center h-screen">
            <Image
                src="/assets/MainLogo.svg"
                alt="Empe One Click Deployer Logo"
                width={400}
                height={100}
                className="w-full md:w-1/2 max-w-[600px]"
            />
            <div className="flex flex-col text-center w-full md:w-1/3 max-w-[600px] gap-6">
                <LinkGradient href={RouterPaths.LOGIN}>{t('start_screen.login')}</LinkGradient>
                <LinkBorder href={RouterPaths.SIGNUP}>{t('start_screen.signup')}</LinkBorder>
            </div>
        </div>
    );
};

export default StartScreen;
