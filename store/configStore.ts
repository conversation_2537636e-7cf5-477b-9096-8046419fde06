import { REQUIRED_CONFIG_KEYS } from '@/config/envConfig';
import { ErrorGetEnvConfig } from '@/errors/ErrorGetEnvConfig';
import { EnvConfig } from '@/types/configEnv';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface State {
    env: EnvConfig | Record<string, string>;
    setEnv: (env: EnvConfig) => void;
}

export const useConfigStore = create<State>()(
    persist(
        set => ({
            env: {},
            setEnv: (env: EnvConfig) => {
                const missingKeys = REQUIRED_CONFIG_KEYS.filter(key => !(key in env));

                if (missingKeys.length > 0) {
                    throw new ErrorGetEnvConfig(`Missing required environment variables: ${missingKeys.join(', ')}`);
                }

                set({ env });
            },
        }),
        {
            name: 'config-store',
            partialize: state => ({
                env: state.env,
            }),
        }
    )
);
