import { isAuthenticated as isAuthenticatedFn } from '@/common/isAuthenticated';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface State {
    token: string | null;
    refreshToken: string | null;
    setAuthTokens: (token: string, refreshToken: string) => void;
    clearAuthTokens: () => void;
    getIsAuthenticated: () => boolean;
}

export const useAuthStore = create<State>()(
    persist(
        (set, get) => ({
            token: null,
            refreshToken: null,
            setAuthTokens: (token, refreshToken) => set({ token, refreshToken }),
            clearAuthTokens: () => set({ token: null, refreshToken: null }),
            getIsAuthenticated: () => {
                const token = get().token;
                const auth = isAuthenticatedFn(token);
                if (!auth) get().clearAuthTokens();
                return auth;
            },
        }),
        {
            name: 'auth-storage',
            partialize: state => ({
                token: state.token,
                refreshToken: state.refreshToken,
            }),
        }
    )
);
