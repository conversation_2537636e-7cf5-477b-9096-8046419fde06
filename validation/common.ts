import { PasswordData } from '@/types/validationMethods';
import { z } from 'zod';

export const validateDeploymentName = (t: (key: string) => string) => {
    return z
        .string()
        .min(3, t('validation_messages.name_required'))
        .regex(/^[a-z][a-z0-9_-]*[a-z0-9]$/, t('validation_messages.name_format'));
};

export const validateName = (t: (key: string) => string) => {
    return z
        .string()
        .min(3, t('validation_messages.name_required'))
        .regex(/^[a-zA-Z]+$/, t('validation_messages.name_format'));
};

export const validateOrganizationName = (t: (key: string) => string) => {
    return z.string().min(1, t('validation_messages.organization_name_required'));
};

export const validateNameWithUppercase = (t: (key: string) => string) => {
    return z
        .string()
        .min(3, t('validation_messages.name_required'))
        .regex(/^[a-zA-Z][a-zA-Z0-9\s_-]*[a-zA-Z0-9]$/, t('validation_messages.name_format'));
};

export const validateEmail = (t: (key: string) => string) => {
    return z.string().min(1, t('validation_messages.email_required')).email(t('validation_messages.email_invalid'));
};

export const validatePassword = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.password_required'))
        .min(8, t('validation_messages.password_length'));
};

export const validateRepeatPassword = (t: (key: string) => string) => {
    return z.string().min(1, t('validation_messages.repeat_password_required'));
};

export const validateVersion = (t: (key: string) => string) => {
    return z
        .union([
            z.string().regex(/^v?(\d+\.\d+(\.\d+)?)$/, t('validation_messages.version_format')),
            z.number().transform(num => num.toString()),
        ])
        .refine(value => value.length > 0, {
            message: t('validation_messages.version_required'),
        });
};

export const validateCheckbox = (t: (key: string) => string) => {
    return z.boolean().refine(value => value === true, {
        message: t('validation_messages.checkbox_required'),
    });
};

export const validateVersionId = (t: (key: string) => string) => {
    return z.number().min(1, t('validation_messages.version_required'));
};

export const validatePasswordsMatch = (t: (key: string) => string) => (data: PasswordData, ctx: z.RefinementCtx) => {
    if (data.password !== data.repeatPassword) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['repeatPassword'],
            message: t('validation_messages.passwords_must_match'),
        });
    }
};

export const validateBlockchainConfig = (t: (key: string) => string) => {
    return z.number().min(1, t('validation_messages.blockchain_config_required'));
};
