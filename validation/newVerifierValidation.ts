import { z } from 'zod';
import { validateBlockchainConfig, validateDeploymentName, validateVersionId } from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const createNewVerifierSchema = (t: (key: string) => string) =>
    z.object({
        verifierName: validateDeploymentName(t),
        versionId: validateVersionId(t),
        blockchainConfigurationId: validateBlockchainConfig(t),
    });

export type NewVerifierForm = z.infer<ReturnType<typeof createNewVerifierSchema>>;

export const NewVerifierFormInputsTypes: Record<keyof NewVerifierForm, FormFieldsTypes> = {
    verifierName: FormFieldsTypes.INPUT,
    versionId: FormFieldsTypes.SELECT,
    blockchainConfigurationId: FormFieldsTypes.SELECT,
};
