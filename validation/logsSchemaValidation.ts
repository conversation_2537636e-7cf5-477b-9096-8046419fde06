import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';

/** Minimal schema: startTime & endTime as ISO-string values (no extra validation) */
export const createGetLogsSchema = () =>
    z.object({
        search: z.string().optional(),
        startTime: z.string().optional(), // e.g. "2025-06-13T09:30"
        endTime: z.string().optional(), // e.g. "2025-06-13T10:45"
    });

export type GetLogsForm = z.infer<ReturnType<typeof createGetLogsSchema>>;

/** Tell your form renderer which UI component to use */
export const TimeRangeFormInputsTypes: Record<keyof GetLogsForm, FormFieldsTypes> = {
    search: FormFieldsTypes.INPUT,
    startTime: FormFieldsTypes.DATETIME,
    endTime: FormFieldsTypes.DATETIME,
};
