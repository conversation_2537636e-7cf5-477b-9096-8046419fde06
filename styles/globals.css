@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
    height: 100%;
}

.without-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.bg-image-logo {
    position: fixed;
    background-image: url('/img/empe-logo-bg.png');
    background-size: 20%;
    background-position: center;
    background-repeat: no-repeat;
}

.markdown h1 {
    @apply text-3xl mb-6 flex justify-center items-center;
}
.markdown h2 {
    @apply text-2xl font-semibold mb-3;
}
.markdown p {
    @apply text-lg leading-7 mb-4;
}
.markdown ul {
    @apply list-disc list-inside mb-4;
}
.markdown li {
    @apply text-lg leading-7;
}
.markdown a {
    @apply text-blue-400 hover:underline py-10 font-semibold;
}

/* for WebKit browsers (Chrome, Safari, Edge, etc) */
input[type='datetime-local']::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(2);
}

/* for Firefox */
input[type='datetime-local']::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(2);
}
