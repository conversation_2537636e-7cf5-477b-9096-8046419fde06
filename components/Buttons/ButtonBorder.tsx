import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorder = ({ children, isLoading, ...props }: Props) => {
    return (
        <button
            className="border-gradient disabled:opacity-50 disabled:cursor-not-allowed w-full text-center whitespace-nowrap text-main-600 py-3 px-6 rounded-md"
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
