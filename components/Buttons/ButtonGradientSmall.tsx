import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonGradientSmall = ({ children, isLoading, ...props }: Props) => {
    return (
        <button
            className={
                'item-gradient disabled:opacity-50 disabled:cursor-not-allowed w-full text-sm text-center whitespace-nowrap py-1 text-white px-6 rounded-xl'
            }
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
