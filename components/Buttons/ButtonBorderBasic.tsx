import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorderBasic = ({ children, isLoading, ...props }: Props) => {
    return (
        <button className="border-2 border-white rounded-md px-10 py-4 text-sm font-bold w-full text-center" {...props}>
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
