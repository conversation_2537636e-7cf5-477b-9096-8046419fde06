'use client';

import { SelectValue } from '@/types/selectValue';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { SelectHTMLAttributes, useState } from 'react';
import { useController, UseControllerProps } from 'react-hook-form';

interface Props extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'defaultValue' | 'name'>, UseControllerProps {
    name: string;
    defaultValue?: string;
    placeholder?: string;
    label?: string;
    disabled?: boolean;
    options: Array<SelectValue>;
}

export const SelectInput = ({
    className,
    label,
    options,
    placeholder,
    control,
    name,
    defaultValue,
    rules,
    disabled,
}: Props) => {
    const {
        field: { value, onChange, ref },
    } = useController({ name, control, defaultValue, rules });
    const t = useTranslations();
    const placeholderText = placeholder ?? t('select_placeholder');
    const [isOpen, setIsOpen] = useState(false);
    const showValue = value ? options.find(option => option.value === value) : null;

    const handleOpen = () => setIsOpen(prev => !prev);

    const handleSelect = (value: string | number) => {
        onChange(value);
        setIsOpen(false);
    };

    const elementLabel = label && <label className="block text-main-800 text-sm">{label}</label>;

    const elementInput = (
        <div
            onClick={handleOpen}
            className={clsx(
                value ? 'text-main-600' : 'text-main-800',
                'disabled:opacity-80 cursor-pointer',
                'focus:outline-none focus:border-main-300',
                'bg-main-1200 border w-full pr-10 border-main-800 rounded-lg p-3',
                className
            )}
            ref={ref}
            aria-disabled={disabled}
        >
            <div className="flex flex-row justify-between items-center">
                {showValue?.label || placeholderText}
                <Image
                    height={20}
                    width={20}
                    alt=""
                    src="/icons/arrow.svg"
                    className={clsx('transition-transform duration-700', isOpen ? 'rotate-180' : 'rotate-0')}
                />
            </div>
        </div>
    );

    const elementDropdown = isOpen && (
        <div
            className={clsx(
                'absolute top-14 z-10 left-0 w-full',
                'bg-main-400 border text-main-600 border-main-800 rounded-lg p-3',
                className
            )}
        >
            {options.map((option, index) => (
                <div
                    key={`${option.value}-${index}`}
                    className="cursor-pointer"
                    onClick={() => handleSelect(option.value)}
                >
                    {option.label}
                </div>
            ))}
        </div>
    );

    return (
        <div className="flex flex-col gap-1 w-full">
            {elementLabel}
            <div className="relative w-full">
                {elementInput}
                {elementDropdown}
            </div>
        </div>
    );
};
