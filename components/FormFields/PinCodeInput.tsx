import { InputHTMLAttributes, ReactNode, useEffect, useRef, useState } from 'react';
import clsx from 'clsx';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value' | 'defaultValue'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    onChange?: (e: { target: { name?: string; value: string } }) => void; // react-hook-form compatible
    value?: string;
    defaultValue?: string;
}

export const PinCodeInput = ({
    className,
    disabled,
    label,
    labelNode,
    value = '',
    defaultValue = '',
    onChange,
    ...props
}: Props) => {
    const inputsRefs = useRef<Array<HTMLInputElement | null>>([]);
    const [values, setValues] = useState<string[]>(() => {
        // Initialize with defaultValue if provided, otherwise empty array
        const initialValue = value || defaultValue;
        if (initialValue.length === 6) {
            return initialValue.split('');
        }
        return Array(6).fill('');
    });

    useEffect(() => {
        // Only update if value is controlled (not empty string from default prop)
        if (value && value.length === 6) {
            setValues(value.split(''));
        } else if (value === '' && !defaultValue) {
            // Only clear if no defaultValue is set
            setValues(Array(6).fill(''));
        }
    }, [value, defaultValue]);

    const focusInput = (index: number) => {
        const input = inputsRefs.current[index];
        if (input) {
            input.focus();
            input.select();
        }
    };

    const handleChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
        const digit = e.target.value;

        if (!/^[0-9]?$/.test(digit)) return;

        const newValues = [...values];
        newValues[index] = digit;
        setValues(newValues);

        const pin = newValues.join('');
        onChange?.({
            target: {
                name: props.name,
                value: pin,
            },
        });

        if (digit && index < 5) {
            focusInput(index + 1);
        }
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace') {
            if (values[index]) {
                const newValues = [...values];
                newValues[index] = '';
                setValues(newValues);
                onChange?.({
                    target: {
                        name: props.name,
                        value: newValues.join(''),
                    },
                });
            } else if (index > 0) {
                focusInput(index - 1);
            }
        } else if (e.key === 'ArrowLeft' && index > 0) {
            focusInput(index - 1);
        } else if (e.key === 'ArrowRight' && index < 5) {
            focusInput(index + 1);
        }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('Text').replace(/\D/g, '').slice(0, 6);

        const newValues = pasteData.padEnd(6, '').split('');
        setValues(newValues);

        onChange?.({
            target: {
                name: props.name,
                value: newValues.join(''),
            },
        });

        if (pasteData.length < 6) {
            focusInput(pasteData.length);
        } else {
            inputsRefs.current[5]?.blur();
        }
    };

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="flex gap-2">
                {values.map((digit, index) => (
                    <input
                        key={index}
                        ref={(el: HTMLInputElement | null) => {
                            inputsRefs.current[index] = el;
                        }}
                        type="text"
                        inputMode="numeric"
                        maxLength={1}
                        disabled={disabled}
                        value={digit}
                        onChange={e => handleChange(index, e)}
                        onKeyDown={e => handleKeyDown(index, e)}
                        onPaste={handlePaste}
                        className={clsx(
                            'disabled:opacity-80 text-center',
                            'focus:outline-none focus:border-main-300',
                            'bg-main-1200 border text-main-600 w-12 h-12 border-main-800 rounded-lg text-2xl',
                            className
                        )}
                    />
                ))}
            </div>
        </div>
    );
};
