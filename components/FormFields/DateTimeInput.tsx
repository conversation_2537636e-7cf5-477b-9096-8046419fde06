'use client';

import clsx from 'clsx';
import { InputHTMLAttributes } from 'react';
import { useC<PERSON>roller, UseControllerProps } from 'react-hook-form';

interface Props
    extends Omit<InputHTMLAttributes<HTMLInputElement>, 'defaultValue' | 'name' | 'type'>,
        UseControllerProps {
    name: string;
    defaultValue?: string;
    placeholder?: string;
    label?: string;
    disabled?: boolean;
}

/**
 * DateTimeInput — an HTML5 `datetime-local` field wired to react-hook-form.
 * Includes name and onBlur from RHF so picker updates correctly.
 */
export const DateTimeInput = ({ className, label, control, name, defaultValue, rules, disabled, ...rest }: Props) => {
    const {
        field: { value, onChange, ref, name: fieldName },
    } = useController({ name, control, defaultValue, rules });
    return (
        <div className="flex flex-col gap-1 w-full">
            {label && (
                <label htmlFor={fieldName} className="block text-main-800 text-sm">
                    {label}
                </label>
            )}
            <div className="relative w-full">
                <input
                    id={fieldName}
                    name={fieldName}
                    type="datetime-local"
                    value={value ?? ''}
                    onChange={e => onChange(e.target.value)}
                    className={clsx(
                        value ? 'text-main-600' : 'text-main-800',
                        'disabled:opacity-80 cursor-pointer',
                        'focus:outline-none focus:border-main-300 w-full border',
                        'bg-main-1200 border w-full border-main-800 rounded-lg p-3 h-12',
                        className
                    )}
                    ref={ref}
                    aria-disabled={disabled}
                    disabled={disabled}
                    {...rest}
                />
            </div>
        </div>
    );
};
