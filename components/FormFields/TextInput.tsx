import { InputHTMLAttributes, ReactNode } from 'react';
import clsx from 'clsx';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    labelNode: ReactNode;
    disabled?: boolean;
}

export const TextInput = ({ className, disabled, label, labelNode, ...props }: Props) => {
    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="relative">
                <input
                    {...props}
                    disabled={disabled}
                    className={clsx(
                        'disabled:opacity-80',
                        'focus:outline-none focus:border-main-300',
                        'bg-main-1200 border text-main-600 w-full pr-10 border-main-800 rounded-lg p-3 h-12',
                        className
                    )}
                />
            </div>
        </div>
    );
};
