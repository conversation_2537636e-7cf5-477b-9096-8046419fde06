'use client';

import { InputHTMLAttributes, useState } from 'react';
import clsx from 'clsx';
import Image from 'next/image';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
    label?: string;
}

export const PasswordInput = ({ className, label, disabled, ...props }: Props) => {
    const [showPassword, setShowPassword] = useState(false);

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            <div className="relative">
                <input
                    {...props}
                    disabled={disabled}
                    type={showPassword ? 'text' : 'password'}
                    className={clsx(
                        'disabled:opacity-80',
                        'focus:outline-none focus:border-main-300',
                        'bg-main-1200 border text-main-600 w-full pr-10 border-main-800 rounded-lg p-3 h-12',
                        className
                    )}
                />
                <Image
                    src={`/assets/${showPassword ? 'EyeOff.svg' : 'EyeOn.svg'}`}
                    alt={showPassword ? 'Ukryj hasło' : '<PERSON><PERSON><PERSON> hasło'}
                    onClick={!disabled ? () => setShowPassword(!showPassword) : undefined}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1"
                    width={28}
                    height={28}
                />
            </div>
        </div>
    );
};
