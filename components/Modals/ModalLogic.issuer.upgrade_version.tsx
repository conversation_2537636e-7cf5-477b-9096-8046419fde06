import { useTranslations } from 'next-intl';
import { ButtonGradient } from '../Buttons';
import { useUpgradeIssuerVersion } from '@/hooks/useUpgradeIssuerVersion';
import { renderFormFields } from '@/common/renderFormFields';
import { UpgradeIssuerVersionFormInputsTypes } from '@/validation/upgradeIssuerVersionValidation';
import { LoaderSpinner } from '../Loaders';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { DeploymentType } from '@/types/deployments';

interface Props {
    deploymentId: number;
    deploymentCurrentVersion: string;
}

const LOCALE_KEY = 'logic_issuer_upgrade_version';

export const ModalLogicIssuerUpgradeVersion = ({ deploymentId, deploymentCurrentVersion }: Props) => {
    const t = useTranslations('modals');
    const { trigger, formFields, register, handleSubmit, errors, isValid, onSubmit, isSubmitting, control } =
        useUpgradeIssuerVersion({ deploymentId });
    const { versions } = useGetAvailableVersions({
        type: DeploymentType.ISSUER,
        deploymentCurrentVersion,
    });

    if (!versions.length) {
        return (
            <div>
                <LoaderSpinner />
            </div>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: UpgradeIssuerVersionFormInputsTypes,
        trigger,
        control,
        selectOptions: {
            version: versions,
        },
    });

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex text-left flex-col gap-4 p-6">
            <h2 className="text-2xl text-center font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            {fieldsToRender}
            <ButtonGradient disabled={!isValid} isLoading={isSubmitting}>
                {t(`${LOCALE_KEY}_button`)}
            </ButtonGradient>
        </form>
    );
};
