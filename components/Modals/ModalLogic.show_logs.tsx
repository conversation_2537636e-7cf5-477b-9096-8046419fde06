import { useTranslations } from 'next-intl';
import { Button<PERSON>order, ButtonBorderSmall, ButtonGradient, ButtonGradientSmall } from '../Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { DeploymentType } from '@/types/deployments';
import { useShowLogs } from '@/hooks/useShowLogs';
import { TimeRangeFormInputsTypes } from '@/validation/logsSchemaValidation';
import { useLogsStore } from '@/store/logsStore';
import { LogEntry } from '../Logs';
import { useCopyAndDownload } from '@/hooks/useCopyAndDownload';
import { LoaderSpinnerSmall } from '../Loaders';

interface Props {
    deploymentId: number;
    type: DeploymentType;
}

const LOCALE_KEY = 'show_logs';

export const ModalLogicShowLogs = ({ deploymentId, type }: Props) => {
    const t = useTranslations('modals');
    const { logs } = useLogsStore();

    const {
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        onSubmit,
        isSubmitting,
        control,
        onReset,
        isLoading,
    } = useShowLogs({ type, deploymentId });

    const { handleCopyToClipboard, handleDownloadFile } = useCopyAndDownload();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: TimeRangeFormInputsTypes,
        trigger,
        control,
    });

    const renderLogs = () => {
        if (isLoading)
            return (
                <div className="flex flex-row justify-center items-center py-16">
                    <LoaderSpinnerSmall />
                </div>
            );
        if (logs.length === 0) return <p className="text-center text-gray-400">{t(`${LOCALE_KEY}_no_logs`)}</p>;
        return (
            <>
                {logs.map((log, index) => (
                    <div key={index} className="w-full break-all">
                        <LogEntry log={log} />
                    </div>
                ))}
            </>
        );
    };

    const renderFooter = () => {
        if (logs.length === 0) return null;
        return (
            <div className="flex flex-row items-center justify-center pt-3 gap-4">
                <div>
                    <ButtonGradientSmall onClick={() => handleCopyToClipboard(logs)}>
                        {t(`${LOCALE_KEY}_button_copy`)}
                    </ButtonGradientSmall>
                </div>
                <div>
                    <ButtonBorderSmall onClick={() => handleDownloadFile(logs, 'logs')}>
                        {t(`${LOCALE_KEY}_button_download`)}
                    </ButtonBorderSmall>
                </div>
            </div>
        );
    };

    return (
        <div className="flex flex-col p-4 h-[90vh] w-[90vw] max-h-[90vh] md:w-auto md:h-auto">
            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 text-left">
                <h2 className="text-xl sm:text-2xl text-center font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
                <div>
                    <div className="flex flex-col-reverse w-full md:flex-row gap-4 items-stretch justify-between">
                        <div className="flex flex-row justify-stretch gap-4 w-full">{fieldsToRender.splice(1, 2)}</div>
                        <div className="flex flex-row items-end pb-3 gap-4">
                            <ButtonGradient disabled={!isValid} isLoading={isSubmitting}>
                                {t(`${LOCALE_KEY}_button`)}
                            </ButtonGradient>
                            <ButtonBorder disabled={!isValid} type="button" onClick={onReset}>
                                {t(`${LOCALE_KEY}_button_clear`)}
                            </ButtonBorder>
                        </div>
                    </div>
                    {fieldsToRender[0]}
                </div>
            </form>
            <div className="overflow-auto mt-4 gap-2 flex flex-col without-scrollbar w-full text-left px-4 py-4 bg-gray-900 rounded-lg">
                {renderLogs()}
            </div>
            {renderFooter()}
        </div>
    );
};
