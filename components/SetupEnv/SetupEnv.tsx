'use client';

import { useSetupEnv } from '@/hooks/useSetupEnv';
import { useConfigStore } from '@/store/configStore';
import { ReactNode } from 'react';
import { LoaderSpinner } from '../Loaders';
import { REQUIRED_CONFIG_KEYS } from '@/config/envConfig';

interface Props {
    children: ReactNode;
}
export const SetupEnv = ({ children }: Props) => {
    const { env } = useConfigStore();
    const missingKeys = REQUIRED_CONFIG_KEYS.filter(key => !(key in env));
    useSetupEnv();

    if (missingKeys.length > 0 || Object.keys(env).length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-screen">
                <LoaderSpinner />
            </div>
        );
    }

    return children;
};
