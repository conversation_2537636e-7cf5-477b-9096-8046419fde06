'use client';

import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export const LogoutButton = () => {
    const t = useTranslations('dashboard');
    const router = useRouter();
    const { clearAuthTokens } = useAuthStore();
    const { clearUser } = useUserStore();

    const handleLogout = () => {
        clearAuthTokens();
        clearUser();
        router.replace('/');
    };

    return (
        <button className="flex items-center gap-2" onClick={handleLogout}>
            <Image src="/assets/Logout.svg" alt="logout" width={24} height={24} />
            <span className="text-main-600">{t('logout')}</span>
        </button>
    );
};
