'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

export const Breadcrumb = () => {
    const t = useTranslations();
    const pathname = usePathname();
    const router = useRouter();

    const rawSegments = pathname.split('/').filter(Boolean);
    const pathSegments = pathname.startsWith('/dashboard') ? rawSegments : ['start', ...rawSegments];

    // Akumulator do budowania pełnej ścieżki
    let accumulatedPath: string[] = [];

    return (
        <div className="flex items-center text-main-600 gap-2 cursor-pointer">
            {pathSegments.map((segment, index) => {
                accumulatedPath.push(segment);

                // Pomiń wyświetlanie segmentów będących liczbami (ID)
                const isId = /^\d+$/.test(segment);
                if (isId) return null;

                const isLast = index === pathSegments.length - 1;
                const href = '/' + accumulatedPath.join('/');

                const content = isLast ? (
                    <span className="text-main-100 underline">{t(`breadcrumb.${segment}`, { default: segment })}</span>
                ) : (
                    <Link href={href} className="hover:underline">
                        {t(`breadcrumb.${segment}`, { default: segment })}
                    </Link>
                );

                const backArrow = !isLast && (
                    <div onClick={() => router.back()} className="cursor-pointer">
                        <Image src="/assets/BackArrow.svg" alt="back arrow" width={24} height={24} />
                    </div>
                );

                return (
                    <div key={href} className="flex items-center gap-2">
                        {content}
                        {backArrow}
                    </div>
                );
            })}
        </div>
    );
};
