'use client';

import Script from 'next/script';
import { useUserStore } from '@/store/userStore';
import { useConfigStore } from '@/store/configStore';

export function PricingTable() {
    const { user } = useUserStore();

    const { env } = useConfigStore.getState();

    return (
        <>
            {/* ⚠️ FIX 2 – load the correct script only on the client */}
            <Script src="https://js.stripe.com/v3/pricing-table.js" strategy="afterInteractive" />

            {/* web-component upgrades itself once the script is in */}
            <stripe-pricing-table
                pricing-table-id={env.NEXT_PUBLIC_STRIPE_PRICING_TABLE_ID!}
                publishable-key={env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!}
                customer-email={user?.email ?? ''}
                style={{ width: '1000px' }}
            />
        </>
    );
}
