import { useTranslations } from 'next-intl';
import { ButtonBorderSmall, ButtonGradientSmall } from '../Buttons/';
import Image from 'next/image';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { DeploymentType } from '@/types/deployments';

interface Props {
    type: DeploymentType;
    deploymentId: number;
    version: string;
    upgradeSecret: () => void;
    upgradeVersion: () => void;
    deleteDeployment: () => void;
}

export const DetailsHeader = ({ type, version, upgradeSecret, upgradeVersion, deleteDeployment }: Props) => {
    const t = useTranslations('details_of_deployment');
    const { versions } = useGetAvailableVersions({
        type: type,
        deploymentCurrentVersion: version,
    });

    const upgrade =
        versions.length > 0 ? (
            <ButtonBorderSmall onClick={upgradeVersion}>{t('upgrade_version_button')}</ButtonBorderSmall>
        ) : null;

    return (
        <div className="flex justify-end items-center flex-col md:flex-row gap-2 lg:gap-4">
            <ButtonGradientSmall onClick={upgradeSecret}>{t('new_secret_button')}</ButtonGradientSmall>
            {upgrade}
            <Image
                src="/assets/Trash.svg"
                alt="Delete"
                className="absolute lg:static top-4 right-4 h-6 w-6 lg:h-6 lg:w-6"
                width={48}
                height={48}
                onClick={deleteDeployment}
            />
        </div>
    );
};
