'use client';

import { useTranslations } from 'next-intl';
import React<PERSON><PERSON> from 'react-json-view';
import { DetailsHeader } from './DetailsHeader';
import { LinkGradientSmall } from '../Links';
import { Deployment, DeploymentStatus, DeploymentType } from '@/types/deployments';
import { isJson } from '@/common/isJson';
import { ButtonBorderSmall } from '../Buttons';
import { ModalLogicShowLogs } from '@/components/Modals';
import { useModal } from '@/hooks/useModal';
import { useLogsStore } from '@/store/logsStore';
import { ReactNode, useEffect } from 'react';

interface Props {
    type: DeploymentType;
    status: DeploymentStatus;
    deployment: Deployment;
    header?: ReactNode;
    handleDelete: () => void;
    handleUpgradeSecret: () => void;
    handleUpgradeVersion: () => void;
}

const API_DOCS_PREFIX = '/api-docs';

const formatKey = (key: string): string => {
    const withSpaces = key.replace(/([a-z0-9])([A-Z])/g, '$1 $2').replace(/[_-]+/g, ' ');
    return withSpaces
        .split(' ')
        .map(w => w.charAt(0).toUpperCase() + w.slice(1))
        .join(' ');
};

const getInstructionLink = (type: DeploymentType): string => {
    return `https://docs.empe.io/technical-docs-staging/develop/${type}`;
};

export const WidgetDetailsOfDeployment = ({
    type,
    deployment,
    status,
    header,
    handleDelete,
    handleUpgradeSecret,
    handleUpgradeVersion,
}: Props) => {
    const t = useTranslations('details_of_deployment');
    const isDeleted = status === DeploymentStatus.DELETED_SCHEDULED;
    const interactionButtons = !isDeleted && (
        <DetailsHeader
            type={type}
            deploymentId={deployment.id}
            version={deployment.version}
            upgradeSecret={handleUpgradeSecret}
            upgradeVersion={handleUpgradeVersion}
            deleteDeployment={handleDelete}
        />
    );

    const { showModal } = useModal();
    const { setLogs } = useLogsStore();

    useEffect(() => {
        setLogs([]);
    }, [setLogs]);

    const handleShowModalLogicShowLogs = () => {
        showModal(<ModalLogicShowLogs deploymentId={deployment.id} type={type} />, true);
    };

    return (
        <div className="flex flex-col h-full justify-between gap-8 py-6">
            {header}
            <div className="flex items-center justify-between rounded-md pl-10 pr-4 flex-col gap-8 lg:flex-row">
                <div className="text-2xl whitespace-nowrap">{t(`${type}_title`)}</div>
                {interactionButtons}
            </div>
            <div className="flex flex-col h-full overflow-auto without-scrollbar">
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('id')}</div>
                    <span className="text-md">{deployment.id}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('fullHost')}</div>
                    <span className="text-md">{deployment.fullHost}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('swaggerApi')}</div>
                    <a
                        className="text-md text-blue-500 hover:text-blue-600"
                        target="_blank"
                        href={deployment.fullHost + API_DOCS_PREFIX}
                    >
                        {deployment.fullHost + API_DOCS_PREFIX}
                    </a>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('status')}</div>
                    <span className="text-md">{deployment.status}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('networkName')}</div>
                    <span className="text-md">{deployment.networkName}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs">{formatKey('version')}</div>
                    <span className="text-md">{deployment.version}</span>
                </div>
                {deployment.issuerName && (
                    <>
                        <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                            <div className="text-xs">{formatKey('issuerName')}</div>
                            <span className="text-md">{deployment.issuerName}</span>
                        </div>
                        <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                            <div className="text-xs">{formatKey('issuerCreatorAddress')}</div>
                            <span className="text-md">{deployment.issuerCreatorAddress}</span>
                        </div>
                        {deployment.didDocument && (
                            <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                                <div className="text-xs">{formatKey('didDocument')}</div>
                                {isJson(deployment.didDocument) ? (
                                    <div className="font-mono text-sm">
                                        <ReactJson
                                            src={JSON.parse(deployment.didDocument)}
                                            theme="twilight"
                                            style={{ background: 'transparent' }}
                                            displayDataTypes={false}
                                            collapsed={8}
                                            name={false}
                                        />
                                    </div>
                                ) : (
                                    <span className="text-md">{deployment.didDocument}</span>
                                )}
                            </div>
                        )}
                    </>
                )}
                {deployment.verifierName && (
                    <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                        <div className="text-xs">{formatKey('verifierName')}</div>
                        <span className="text-md">{deployment.verifierName}</span>
                    </div>
                )}
            </div>
            <div className="flex justify-center items-center md:w-1/2 mx-auto gap-4">
                <LinkGradientSmall href={getInstructionLink(type)} target="_blank">
                    {t('instruction')}
                </LinkGradientSmall>
                <ButtonBorderSmall onClick={handleShowModalLogicShowLogs}>{t('get_logs_button')}</ButtonBorderSmall>
            </div>
        </div>
    );
};
