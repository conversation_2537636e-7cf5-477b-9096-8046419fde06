import { useController, UseControllerProps } from 'react-hook-form';
import clsx from 'clsx';
import { InputHTMLAttributes } from 'react';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'defaultValue'>, UseControllerProps {
    name: string;
    defaultValue?: boolean;
    mainLabel: string;
    label?: string;
    disabled?: boolean;
    href?: string;
}

export const Checkbox = ({
    name,
    control,
    defaultValue = false,
    rules,
    label,
    mainLabel,
    disabled = false,
    className,
    href,
    ...rest
}: Props) => {
    const {
        field: { value, onChange, ref },
    } = useController({ name, control, defaultValue, rules });

    const handleToggle = () => {
        if (!disabled) {
            const newValue = !value;
            onChange(newValue);
        }
    };

    const elementLabel = label && <label className="block text-main-800 text-sm mb-1">{label}</label>;

    const elementInput = (
        <div
            className={clsx('flex items-center gap-2 cursor-pointer', disabled && 'opacity-50 cursor-not-allowed')}
            onClick={handleToggle}
        >
            <input
                type="checkbox"
                checked={value}
                ref={ref}
                {...rest}
                className={clsx(
                    'w-6 h-6 border-2 rounded-md',
                    value ? 'bg-main-100 border-none' : 'bg-transparent border-main-800',
                    disabled && 'cursor-not-allowed'
                )}
            />
            <span className={clsx('text-main-100 underline', className)}>
                <a href={href} target="_blank">
                    {mainLabel}
                </a>
            </span>
        </div>
    );

    return (
        <div className="flex flex-col w-full">
            {elementInput}
            {elementLabel}
        </div>
    );
};
