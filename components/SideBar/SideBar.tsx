'use client';
import { useUserStore } from '@/store/userStore';
import { LogoutButton } from '../LogoutButton';
import Image from 'next/image';
import { fetchPortalUrl } from '@/api/stripePortal';
import { useCallback } from 'react';
import { RouterPaths } from '@/common/routerPaths';
import Link from 'next/link';
import { useSideBar } from '@/contexts/SideBarContext';
import { useRouter } from 'next/navigation';

export const SideBar = () => {
    const { user } = useUserStore();
    const { isOpen, closeSideBar } = useSideBar();
    const router = useRouter();

    const handlePortalClick = useCallback(async () => {
        try {
            const url = await fetchPortalUrl();
            if (!url) {
            } else {
                window.open(url, '_blank');
            }
        } catch {
            router.replace(RouterPaths.PAYMENT);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const resetPasswordUrl = `${RouterPaths.REQUEST_PASSWORD_RESET}?email=${user?.email}`;

    if (!isOpen) return null;

    return (
        <div className="h-screen !fixed right-0 md:!relative border border-gradient z-10 p-8 w-72 flex flex-col justify-between !bg-main-1200">
            <Image
                src="/assets/CloseIcon.svg"
                alt="logo"
                className="cursor-pointer self-end block md:hidden"
                onClick={closeSideBar}
                width={16}
                height={16}
            />
            <div className="gap-4 flex flex-col">
                <div className="flex flex-row gap-4 items-center text-main-600">
                    <Image src="/assets/EmailIcon.svg" alt="Logo" width={24} height={24} />
                    <span>{user?.email}</span>
                </div>
                <div className="border-b border-main-900" />
                <Link href={resetPasswordUrl} className="flex flex-row gap-4 items-center text-main-600">
                    <Image src="/assets/PasswordIcon.svg" alt="Logo" width={24} height={24} />
                    <span>Change Password</span>
                </Link>
                <div className="border-b border-main-900" />
                <button onClick={handlePortalClick} className="flex flex-row gap-4 items-center text-main-600">
                    <Image src="/assets/WalletIcon.svg" alt="Logo" width={24} height={24} />
                    <span>Manage Subscription</span>
                </button>
            </div>
            <div className="flex flex-row justify-center items-center">
                <LogoutButton />
            </div>
        </div>
    );
};
