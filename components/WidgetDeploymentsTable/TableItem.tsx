import { type Deployment } from '@/types/deployments';
import { TableItemStatus } from './TableItemStatus';
import { useTranslations } from 'next-intl';
import { LinkBorderSmall } from '../Links/LinkBorderSmall';
import { RouterPaths } from '@/common/routerPaths';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

interface Props {
    deployment: Deployment;
}

const networkLabels: Record<BlockchainNetworkName, string> = {
    MAINNET: 'Mainnet',
    TESTNET: 'Testnet',
};

export const TableItem = ({ deployment }: Props) => {
    const t = useTranslations();
    const name = deployment.issuerName || deployment.verifierName;
    const path = deployment.issuerName ? RouterPaths.ISSUER_DETAILS : RouterPaths.VERIFIER_DETAILS;

    const role = deployment.issuerName ? 'Issuer' : 'Verifier';
    const typeLabel = `${role} ${networkLabels[deployment.networkName]}`;

    return (
        <tr className="border-collapse h-14 [&>td]:border-main-800/20 [&>td]:border [&>td]:px-4 [&>td]:py-1">
            <td>{name}</td>
            <td>{typeLabel}</td>
            <td>
                <TableItemStatus status={deployment.status} />
            </td>
            <td className="text-center">
                <LinkBorderSmall href={`${path}/${deployment.id}`}>
                    {t('deployments_table.details_button')}
                </LinkBorderSmall>
            </td>
        </tr>
    );
};
