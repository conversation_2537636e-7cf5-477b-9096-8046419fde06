import { DeploymentStatus, DeploymentStatusColors } from '@/types/deployments';
import clsx from 'clsx';

interface Props {
    status: DeploymentStatus;
}
export const TableItemStatus = ({ status }: Props) => {
    return (
        <div className="flex items-center justify-start gap-2">
            <div className={clsx(DeploymentStatusColors[status], 'h-3 w-3 rounded-full')} />
            <span>{status}</span>
        </div>
    );
};
