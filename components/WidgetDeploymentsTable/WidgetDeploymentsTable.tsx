import { type Deployment } from '@/types/deployments';
import { TableItem } from './TableItem';
import { useTranslations } from 'next-intl';
import { EmptyTableInfo } from './EmptyTableInfo';
import { LoaderSpinner } from '../Loaders';

interface Props {
    deployments: Deployment[];
    isLoading: boolean;
}

const EmptyWidgetDeploymentsTable = () => {
    const t = useTranslations();

    return (
        <div className="w-full h-full flex gap-4 flex-col text-main-600 p-4">
            <h1 className="flex flex-1 text-lg">{t('deployments_table.widget_title')}</h1>
            <div className="flex-[2] flex justify-center items-center">
                <EmptyTableInfo />
            </div>
        </div>
    );
};

export const WidgetDeploymentsTable = ({ deployments, isLoading }: Props) => {
    const t = useTranslations();

    if (isLoading) {
        return (
            <div className="w-full h-full flex justify-center items-center">
                <LoaderSpinner />
            </div>
        );
    }

    if (deployments.length === 0) {
        return <EmptyWidgetDeploymentsTable />;
    }

    return (
        <div className="w-full text-main-600 h-full overflow-hidden">
            <h1 className="p-4 text-lg">{t('deployments_table.widget_title')}</h1>
            <div className="h-full overflow-y-auto without-scrollbar scroll-smooth">
                <table className="w-full">
                    <thead>
                        <tr className="text-left [&>th]:px-4 [&>th]:py-1">
                            <th>{t('deployments_table.name')}</th>
                            <th>{t('deployments_table.type')}</th>
                            <th>{t('deployments_table.status')}</th>
                            <th />
                        </tr>
                    </thead>
                    <tbody>
                        {deployments.map((item, index) => {
                            return <TableItem deployment={item} key={`deploy-${index}-${item.id}`} />;
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
