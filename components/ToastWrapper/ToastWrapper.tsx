import { useToast } from '@/hooks/useToast';
import Image from 'next/image';
import { ReactNode, useEffect } from 'react';

interface Props {
    children: ReactNode;
}
export const ToastWrapper = ({ children }: Props) => {
    const { hideToast } = useToast();

    useEffect(() => {
        if (children) {
            const timer = setTimeout(() => {
                hideToast();
            }, 6000);

            return () => clearTimeout(timer);
        }
    }, [children, hideToast]);

    if (!children) return null;
    return (
        <div className="fixed z-20 top-10 left-0 w-full flex justify-center items-center">
            <div className="p-4 bg-main-1000 rounded-lg relative min-w-[300px]">
                <Image
                    src="/icons/toast_exit.svg"
                    alt="Toast Icon"
                    className="absolute top-3 right-3 cursor-pointer"
                    width={12}
                    height={12}
                    onClick={hideToast}
                />
                {children}
            </div>
        </div>
    );
};
