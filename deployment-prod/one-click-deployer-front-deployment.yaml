apiVersion: apps/v1
kind: Deployment
metadata:
    name: one-click-deployer-front
    namespace: evdi-production-one-click-deployment
spec:
    replicas: 1
    selector:
        matchLabels:
            app: one-click-deployer-front
    template:
        metadata:
            labels:
                app: one-click-deployer-front
        spec:
            containers:
                - name: one-click-deployer-front
                  image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer-front:0.0.41
                  imagePullPolicy: Always
                  ports:
                      - containerPort: 3000
                  volumeMounts:
                      - name: app-config-one-click-deployer
                        mountPath: /public/config
                        readOnly: true
            imagePullSecrets:
                - name: harbor-registry-secret
            volumes:
                - name: kubeconfig-volume
                  secret:
                      secretName: kubeconfig-secret
                - name: app-config-one-click-deployer
                  configMap:
                      name: app-config-one-click-deployer
