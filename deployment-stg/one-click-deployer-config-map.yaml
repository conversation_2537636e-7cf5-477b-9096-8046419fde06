apiVersion: v1
data:
    env.json: |
        {
          "API_URL": "https://deploy-stg.evdi.app/api",
          "DEPLOYMENT_TYPE": "EMPE_OVH_K8S_DEPLOYMENT",
          "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_test_51RWywyDFuVr5fRqFJh3CMDtBq8QmHKXhPDHJZIQl6ISZxCmQcmZkda6IMwUNohiDAmeJu474qYxT5CErOvnyMhOm0073m3insv",
          "NEXT_PUBLIC_STRIPE_PRICING_TABLE_ID": "prctbl_1RWz5HDFuVr5fRqFhrDhrddh"
        }
kind: ConfigMap
metadata:
    name: app-config-one-click-deployer-stg
    namespace: customer-issuer-verifier
