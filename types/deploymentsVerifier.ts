import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { DeploymentStatus } from './deployments';
import { NewVerifierForm } from '@/validation/newVerifierValidation';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

export type VerifierDeploymentCreateRequest = {
    deploymentType: DeploymentsTypesEnum;
} & NewVerifierForm;

export type VerifierDeployment = {
    id: number;
    verifierName: string;
    fullHost: string;
    status: DeploymentStatus;
    userId: number;
    deploymentType: DeploymentsTypesEnum;
    version: string;
    verifierCreatorAddress: string;
    didDocument: string;
    networkName: BlockchainNetworkName;
};

export type VerifierDeploymentCreateResponse = {
    verifierDeployment: VerifierDeployment;
    authKey: string;
};
