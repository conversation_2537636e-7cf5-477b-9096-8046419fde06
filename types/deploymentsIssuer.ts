import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { DeploymentStatus } from './deployments';
import { NewIssuerForm } from '@/validation/newIssuerValidation';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

export type IssuerDeploymentCreateRequest = {
    deploymentType: DeploymentsTypesEnum;
} & NewIssuerForm;

export type IssuerDeployment = {
    id: number;
    issuerName: string;
    fullHost: string;
    status: DeploymentStatus;
    userId: number;
    deploymentType: DeploymentsTypesEnum;
    organization: string;
    version: string;
    issuerCreatorAddress: string;
    didDocument: string;
    networkName: BlockchainNetworkName;
};

export type IssuerDeploymentCreateResponse = {
    issuerDeployment: IssuerDeployment;
    authKey: string;
};
