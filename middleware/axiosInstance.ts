import { handleTokenRefresh } from '@/api/user';
import { useAuthStore } from '@/store/authStore';
import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { useConfigStore } from '@/store/configStore';

const { env } = useConfigStore.getState();

const AUTH_EXCLUDE_ENDPOINTS = ['/auth/login', '/auth/signup', '/auth/refresh'];

interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
    _retry?: boolean;
}

const axiosInstance = axios.create({
    baseURL: env.API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

const refreshAuthToken = async () => {
    try {
        const refreshToken = useAuthStore.getState().refreshToken;
        const response = await handleTokenRefresh(refreshToken);
        const { token, refreshToken: newRefreshToken } = response;
        useAuthStore.getState().setAuthTokens(token, newRefreshToken);
        return token;
    } catch (error) {
        useAuthStore.getState().clearAuthTokens();
        throw error;
    }
};

// Interceptor żądań
axiosInstance.interceptors.request.use(config => {
    const isPublicEndpoint = AUTH_EXCLUDE_ENDPOINTS.some(endpoint => config.url?.includes(endpoint));

    if (!isPublicEndpoint) {
        const token = useAuthStore.getState().token;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }

    return config;
});

// Interceptor odpowiedzi
axiosInstance.interceptors.response.use(
    response => response,
    async (error: AxiosError) => {
        const originalRequest = error.config as ExtendedAxiosRequestConfig;
        const isPublicEndpoint = AUTH_EXCLUDE_ENDPOINTS.some(endpoint => error.config?.url?.includes(endpoint));
        // Sprawdzamy, czy mamy 401 i czy nie ponawialiśmy już żądania
        if (error.response?.status === 401 && !isPublicEndpoint && originalRequest && !originalRequest._retry) {
            originalRequest._retry = true; // Oznaczamy żądanie jako powtórzone

            try {
                const newToken: string = await refreshAuthToken();

                // Ustawiamy nowy token na pierwotnym żądaniu
                if (originalRequest.headers) {
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;
                }

                return axiosInstance(originalRequest); // Powtarzamy żądanie z nowym tokenem
            } catch (refreshError) {
                return Promise.reject(refreshError); // Przerywamy cykl w przypadku błędu odświeżenia
            }
        }

        // Inne błędy - odrzucamy obiekt błędu
        return Promise.reject(error);
    }
);

export default axiosInstance;
