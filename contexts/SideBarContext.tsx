'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface SideBarContextType {
    isOpen: boolean;
    openSideBar: () => void;
    closeSideBar: () => void;
    toggleSideBar: () => void;
}

const SideBarContext = createContext<SideBarContextType | undefined>(undefined);

interface SideBarProviderProps {
    children: ReactNode;
}

export const SideBarProvider = ({ children }: SideBarProviderProps) => {
    const [isOpen, setIsOpen] = useState(false);

    const openSideBar = () => setIsOpen(true);
    const closeSideBar = () => setIsOpen(false);
    const toggleSideBar = () => setIsOpen(prev => !prev);

    const value = {
        isOpen,
        openSideBar,
        closeSideBar,
        toggleSideBar,
    };

    return <SideBarContext.Provider value={value}>{children}</SideBarContext.Provider>;
};

export const useSideBar = () => {
    const context = useContext(SideBarContext);
    if (context === undefined) {
        throw new Error('useSideBar must be used within a SideBarProvider');
    }
    return context;
};
